package com.meishe.myvideo.view.presenter;

import android.content.Context;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.event.MessageEvent;
import com.meishe.myvideo.view.MYMixedModeMenuView;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_MIXED_MODE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_MIXED_MODE_PROGRESS;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_TYPE_MIXED_MODE_PROGRESS_FINISH;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 调整Presenter类
 * mixed mode the Presenter class
 *
 * <AUTHOR> Chu<PERSON>hen<PERSON>uang
 * @CreateDate :2021/06/09 15:22
 * @Description :Presenter of mixed mode menu view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class MixedModePresenter extends BaseConfirmPresenter<MYMixedModeMenuView> {

    @Override
    public void getData(Context context) {
    }

    @Override
    public boolean updateData(int subType, boolean needForceUpdate) {
        return true;
    }

    @Override
    public void onMessageEvent(MessageEvent event) {
        super.onMessageEvent(event);
    }

    @Override
    public void onItemClicked(IBaseInfo info, boolean isSelected) {
        MessageEvent.sendEvent(info, MESSAGE_TYPE_MIXED_MODE);
    }

    @Override
    public void onProgressChanged(float progress, String tag, boolean isFromUser) {
        if (!isFromUser) {
            return;
        }
        MessageEvent messageEvent = new MessageEvent();
        messageEvent.setFloatValue(progress / 100F);
        messageEvent.setEventType(MESSAGE_TYPE_MIXED_MODE_PROGRESS);
        EventBus.getDefault().post(messageEvent);
    }

    @Override
    public void onStopTrackingTouch() {
        MessageEvent messageEvent = new MessageEvent();
        messageEvent.setEventType(MESSAGE_TYPE_MIXED_MODE_PROGRESS_FINISH);
        EventBus.getDefault().post(messageEvent);
    }

    @Override
    public void reset(List<IBaseInfo> data) {

    }

    @Override
    public void confirm() {
    }

    @Override
    public void applyToAll() {
    }
}
