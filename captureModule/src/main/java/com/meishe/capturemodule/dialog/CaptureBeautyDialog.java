package com.meishe.capturemodule.dialog;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.Gravity;
import android.view.View;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.utils.SizeUtils;
import com.meishe.capturemodule.R;
import com.meishe.capturemodule.adapter.BeautyShapeAdapter;
import com.meishe.capturemodule.adapter.SpaceItemDecoration;
import com.meishe.capturemodule.bean.BeautyShapeDataItem;
import com.meishe.capturemodule.view.MagicProgress;

import java.util.List;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/4/13 14:07
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class CaptureBeautyDialog extends Dialog {

    private Context mContext;
    private MagicProgress mShapeSeekBar;
    private BeautyShapeAdapter mShapeAdapter;
    private List<BeautyShapeDataItem> mShapeDataList;
    private boolean isOpen = true;
    private TextView tvBeautyShape;
    private TextView tvBeautyReset;

    public CaptureBeautyDialog(@NonNull Context context, List<BeautyShapeDataItem> shapeDataList) {
        super(context, 0);
        mShapeDataList = shapeDataList;
        mContext = context;
        setContentView(R.layout.capture_beauty_view);
        initData();
        initView();
        initShapeRecyclerView();
    }

    private void initData() {

    }

    private void initShapeRecyclerView() {
        RecyclerView mShapeRecyclerView = (RecyclerView) findViewById(R.id.beauty_shape_item_list);
        mShapeAdapter = new BeautyShapeAdapter(mContext, mShapeDataList);
        LinearLayoutManager linearLayoutManager = new
                LinearLayoutManager(mContext, LinearLayoutManager.HORIZONTAL, false);
        mShapeRecyclerView.setLayoutManager(linearLayoutManager);
        mShapeRecyclerView.setAdapter(mShapeAdapter);
        int space = SizeUtils.dp2px(35);
        mShapeRecyclerView.addItemDecoration(new SpaceItemDecoration(space, 0));
        mShapeAdapter.setOnItemClickListener(new BeautyShapeAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(View view, int position) {
                if (position < 0 || position >= mShapeAdapter.getItemCount()) {
                    return;
                }
                if (tvBeautyReset != null) {
                    tvBeautyReset.setEnabled(true);
                }
                mShapeSeekBar.setVisibility(View.VISIBLE);
                if (mEventListener != null) {
                    mEventListener.onItemClick(view, position);
                }
            }
        });
    }

    private void initView() {
        mShapeSeekBar = (MagicProgress) findViewById(R.id.shape_sb);
        mShapeSeekBar.setMax(100);
        mShapeSeekBar.setBreakProgress(0);
        mShapeSeekBar.setPointEnable(true);
        mShapeSeekBar.setOnProgressChangeListener(new MagicProgress.OnProgressChangeListener() {
            @Override
            public void onProgressChange(int progress, boolean fromUser) {
                if (!fromUser && tvBeautyReset != null) {
                    tvBeautyReset.setEnabled(true);
                }
                if (mEventListener != null) {
                    mEventListener.onProgressChange(progress, fromUser);
                }
            }
        });
        tvBeautyShape = findViewById(R.id.beauty_shape_switch_text);

        tvBeautyShape.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                isOpen = !isOpen;
                setBeautyShapeChecked(isOpen);

            }
        });

        tvBeautyReset = findViewById(R.id.beauty_shape_reset_txt);

        tvBeautyReset.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                tvBeautyReset.setEnabled(false);
                if (mEventListener != null) {
                    mEventListener.onReset();
                }
            }
        });
        setOnDismissListener(new OnDismissListener() {
            @Override
            public void onDismiss(DialogInterface dialogInterface) {
                closeCaptureDialogView();
                if (mEventListener != null) {
                    mEventListener.onDismiss();
                }
            }
        });

        setOnCancelListener(new OnCancelListener() {
            @Override
            public void onCancel(DialogInterface dialogInterface) {
                closeCaptureDialogView();
                if (mEventListener != null) {
                    mEventListener.onCancel();
                }
            }
        });
    }

    private void setBeautyShapeChecked(boolean isOpen) {
        mShapeAdapter.setEnable(isOpen);
        if (!isOpen) {
            tvBeautyShape.setText(R.string.beauty_open);
            mShapeSeekBar.setVisibility(View.INVISIBLE);
            mShapeAdapter.setSelectPos(Integer.MAX_VALUE);
        } else {
            tvBeautyShape.setText(R.string.beauty_close);
        }
        if (mEventListener != null) {
            mEventListener.onBeautyChecked(isOpen);
        }
    }


    /**
     * 设置进度条进度
     * Set progress
     *
     * @param progress the progress
     */
    public void setProgress(int progress) {
        mShapeSeekBar.setProgress(progress);
    }

    /**
     * 设置点位置
     * Set point progress
     *
     * @param progress the progress
     */
    public void setPointProgress(int progress) {
        mShapeSeekBar.setPointProgress(progress);
    }

    public void setChecked(boolean isChecked) {
        setBeautyShapeChecked(isChecked);
    }

    public boolean isChecked() {
        return isOpen;
    }

    /**
     * 展示dialog
     * Show capture dialog view.
     */
    public void showCaptureDialogView() {
        TranslateAnimation translate = new TranslateAnimation(Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF, 1.0f);
        /*
         * 动画时间500毫秒
         *The animation time is 500 ms
         * */
        translate.setDuration(200);
        translate.setFillAfter(false);
        show();
        setCanceledOnTouchOutside(true);
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.MATCH_PARENT;
        params.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setGravity(Gravity.BOTTOM);
        params.dimAmount = 0.0f;
        getWindow().setAttributes(params);
        getWindow().setBackgroundDrawable(ContextCompat.getDrawable(mContext, R.color.colorTranslucent));
        getWindow().setWindowAnimations(R.style.fx_dlg_style);
    }

    public void closeCaptureDialogView() {
        dismiss();
        TranslateAnimation translate = new TranslateAnimation(Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF,
                0.0f, Animation.RELATIVE_TO_SELF,
                1.0f, Animation.RELATIVE_TO_SELF, 0.0f);
        /*
         * 动画时间300毫秒
         *The animation time is 300 ms
         * */
        translate.setDuration(300);
        translate.setFillAfter(false);
    }

    private EventListener mEventListener;

    public void setEventListener(EventListener eventListener) {
        mEventListener = eventListener;
    }

    public BeautyShapeDataItem getSelectItem() {
        if (mShapeAdapter != null) {
            return mShapeAdapter.getSelectItem();
        }
        return null;
    }

    public void setMax(int max) {
        mShapeSeekBar.setMax(max);
    }

    public void setBreakProgress(int progress) {
        mShapeSeekBar.setBreakProgress(progress);
    }

    public void setSeekBarVisible(int invisible) {
        mShapeSeekBar.setVisibility(invisible);
    }

    public interface EventListener {
        void onDismiss();

        void onCancel();

        void onReset();

        void onBeautyChecked(boolean isOpen);

        void onProgressChange(int progress, boolean fromUser);

        void onItemClick(View view, int position);
    }
}
