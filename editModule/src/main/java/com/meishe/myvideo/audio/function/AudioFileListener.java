package com.meishe.myvideo.audio.function;

/**
 * The interface Audio file listener.
 * 此接口为音频文件监听器
 */
public interface AudioFileListener {
    /**
     * 文件保存失败
     * File save failed
     * @param reason 失败的原因
     */
    void onFailure(String reason);

    /**
     * 文件保存成功
     * File saved successfully
     * @param savePath 保存文件的路径
     * @param duration 时长
     */
    void onSuccess(String savePath, long duration);
}
