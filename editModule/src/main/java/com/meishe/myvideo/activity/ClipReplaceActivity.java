package com.meishe.myvideo.activity;

import android.app.Activity;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.fragment.app.FragmentManager;

import com.meicam.sdk.NvsAVFileInfo;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.utils.CommonUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.bean.MeicamVideoTrack;
import com.meishe.engine.editor.EditorController;
import com.meishe.myvideo.R;
import com.meishe.player.fragment.PlayerFragment;
import com.meishe.player.view.MultiThumbnailSequenceView2;
import com.meishe.player.view.TailorView;
import com.meishe.player.view.bean.TailorClip;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: ChuChenGuang
 * @CreateDate: 2022/10/26 14:18
 * @Description: clip替换的时候裁剪 The clip replace activity
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class ClipReplaceActivity extends BaseActivity implements PlayerFragment.PlayEventListener {

    public static final String VIDEO_PATH = "videoPath";
    public static final String VIDEO_LIMIT = "videoLimit";
    public static final String INTENT_TRAM = "intentTrim";
    private LinearLayout mActivityTailorFragmentContainer;
    private PlayerFragment mPlayerFragment;
    private String mVideoPath;
    private long mVideoLimit;
    private TailorView mTailorView;
    private TailorClip mTailorClip;
    private int mState = -1;
    private long mNowStartTime = 0;
    private MeicamTimeline mTimeline;
    private EditorController mEditorController;

    @Override
    protected int bindLayout() {
        return R.layout.activity_clip_replace;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        mEditorController = EditorController.getInstance();
        Bundle extras = getIntent().getExtras();
        if (null != extras) {
            mVideoPath = extras.getString(VIDEO_PATH);
            mVideoLimit = extras.getLong(VIDEO_LIMIT);
        }
        mTimeline = new MeicamTimeline.TimelineBuilder(mEditorController
                .getStreamingContext(), MeicamTimeline.TimelineBuilder.BUILD_NORMAL)
                .setVideoResolution(EditorEngine.getVideoEditResolution(mVideoPath))
                .build();
        mEditorController.setMeicamTimeNoEngine(mTimeline);
        long duration = -1;
        NvsAVFileInfo avFileInfo = NvsStreamingContext.getInstance().getAVFileInfo(mVideoPath);
        if (avFileInfo != null) {
            duration = avFileInfo.getDuration();
        }
        mTailorClip = new TailorClip(mVideoPath, mVideoLimit, 0, duration);
        MeicamVideoTrack meicamVideoTrack = mTimeline.appendVideoTrack();
        meicamVideoTrack.addVideoClip(mVideoPath, 0, 0, duration);
    }

    @Override
    protected void initView() {
        mActivityTailorFragmentContainer = findViewById(R.id.activity_tailor_fragment_container);
        addVideoFragment();
        ImageView mActivityTailorBack = findViewById(R.id.activity_tailor_back);
        mActivityTailorBack.setOnClickListener(v -> {
            onBackPressed();
        });
        Button mActivityTailorSure = findViewById(R.id.activity_tailor_sure);
        mActivityTailorSure.setOnClickListener(v -> {
            Intent intent = new Intent();
            intent.putExtra(INTENT_TRAM, mNowStartTime);
            //设置返回码和返回携带的数据
            //Set the return code and return the data carried
            setResult(Activity.RESULT_OK, intent);
            finish();
        });
        Drawable drawable = CommonUtils.getRadiusDrawable(-1, -1,
                getResources().getDimensionPixelOffset(R.dimen.dp_px_150), getResources().getColor(R.color.activity_tailor_button_background));
        mActivityTailorSure.setBackground(drawable);
        mTailorView = findViewById(R.id.activity_tailor_view);
        mTailorView.setOnScrollListener(new MultiThumbnailSequenceView2.OnScrollListener() {
            @Override
            public void onScrollChanged(int dx, int oldDx) {
                long nowTime = mEditorController.lengthToDuration(dx, mTailorView.getPixelPerMicrosecond());
                mNowStartTime = nowTime;
                mEditorController.seekTimeline(nowTime, 0);
            }

            @Override
            public void onScrollStopped() {
                mEditorController.playNow(mEditorController.nowTime() + mVideoLimit);
            }

            @Override
            public void onSeekingTimeline() {
                mEditorController.stop();
            }
        });
        mTailorView.setTailorClip(mTailorClip);
        TextView activityTailorTextLimit = findViewById(R.id.activity_tailor_text_limit);
        String text = (mVideoLimit / 1000000) + "S";
        activityTailorTextLimit.setText(text);
    }

    private void addVideoFragment() {
        FragmentManager fragmentManager = getSupportFragmentManager();
        mPlayerFragment = PlayerFragment.create();
        mPlayerFragment.setPlayListener(this);
        mPlayerFragment.setTimelineAndContext(mTimeline, mEditorController.getStreamingContext());
        fragmentManager.beginTransaction().add(R.id.activity_tailor_fragment_container, mPlayerFragment).commitAllowingStateLoss();
        fragmentManager.beginTransaction().show(mPlayerFragment);
        /*
         * 此处是等页面加载完毕后再seek
         * Here is seek after the page loads
         * */
        mActivityTailorFragmentContainer.post(() -> mPlayerFragment.seekTimeline(0));
    }

    @Override
    public void onPlayBackPrepare() {

    }

    @Override
    public void playBackEOF(NvsTimeline timeline) {
        mEditorController.playNow(mNowStartTime, mNowStartTime + mVideoLimit);
    }

    @Override
    public void playStopped(NvsTimeline timeline) {

    }

    @Override
    public void playbackTimelinePosition(NvsTimeline timeline, long stamp) {
        mState = TailorView.FROM_VIDEO;
        mTailorView.seekToPosition(stamp, mState, mNowStartTime);
    }

    @Override
    public void streamingEngineStateChanged(int state) {
        if (mEditorController.isPlaying()) {
            mState = TailorView.FROM_VIDEO;
            mTailorView.setState(mState);
        } else {
            mTailorView.setState(TailorView.FROM_USER);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mEditorController.removeTimeline();
    }
}