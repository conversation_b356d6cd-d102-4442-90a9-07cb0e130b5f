package com.meishe.myvideo.ui.trackview;

import com.meishe.myvideo.view.MYEditorTimelineTrackView;

import java.util.List;

/**
 * 时间轴打点功能使用示例
 * Timeline mark feature usage example
 */
public class TimelineMarkUsageExample {
    
    private MYEditorTimelineTrackView mTimelineTrackView;
    
    /**
     * 示例：如何使用时间轴打点功能
     * Example: How to use timeline mark feature
     */
    public void demonstrateTimelineMarkUsage() {
        
        // 1. 添加时间轴标记点
        // Add timeline marks at specific timestamps
        
        // 在5秒处添加标记点 (5秒 = 5,000,000微秒)
        long timestamp5s = 5 * 1000 * 1000; // 5 seconds in microseconds
        mTimelineTrackView.addTimelineMark(timestamp5s);
        
        // 在10秒处添加带标签的标记点
        long timestamp10s = 10 * 1000 * 1000; // 10 seconds in microseconds
        mTimelineTrackView.addTimelineMark(timestamp10s, "重要节点");
        
        // 在15秒处添加另一个标记点
        long timestamp15s = 15 * 1000 * 1000; // 15 seconds in microseconds
        mTimelineTrackView.addTimelineMark(timestamp15s, "结束点");
        
        // 2. 自定义标记点样式
        // Customize mark appearance
        
        // 设置标记点颜色为红色
        mTimelineTrackView.setTimelineMarkColor(0xFFFC2B55);
        
        // 设置标记点尺寸 (宽度8dp, 高度20dp)
        mTimelineTrackView.setTimelineMarkSize(8, 20);
        
        // 3. 管理标记点
        // Manage timeline marks
        
        // 获取所有标记点
        List<TimelineMarkView.TimelineMark> allMarks = mTimelineTrackView.getTimelineMarks();
        System.out.println("当前标记点数量: " + allMarks.size());
        
        // 移除特定时间戳的标记点
        mTimelineTrackView.removeTimelineMark(timestamp5s);
        
        // 清除所有标记点
        // mTimelineTrackView.clearTimelineMarks();
    }
    
    /**
     * 示例：根据用户操作动态添加标记点
     * Example: Dynamically add marks based on user actions
     */
    public void addMarkAtCurrentPosition(long currentTimestamp) {
        // 在当前播放位置添加标记点
        mTimelineTrackView.addTimelineMark(currentTimestamp, "用户标记");
    }
    
    /**
     * 示例：批量添加标记点
     * Example: Batch add multiple marks
     */
    public void addMultipleMarks() {
        // 每隔5秒添加一个标记点
        for (int i = 1; i <= 10; i++) {
            long timestamp = i * 5 * 1000 * 1000; // 每5秒
            mTimelineTrackView.addTimelineMark(timestamp, "标记" + i);
        }
    }
    
    /**
     * 示例：根据视频内容添加语义化标记点
     * Example: Add semantic marks based on video content
     */
    public void addSemanticMarks() {
        // 开场标记
        mTimelineTrackView.addTimelineMark(0, "开场");
        
        // 高潮部分
        long climaxTime = 30 * 1000 * 1000; // 30秒
        mTimelineTrackView.addTimelineMark(climaxTime, "高潮");
        
        // 结尾标记
        long endTime = 60 * 1000 * 1000; // 60秒
        mTimelineTrackView.addTimelineMark(endTime, "结尾");
    }
    
    /**
     * 设置时间轴轨道视图引用
     */
    public void setTimelineTrackView(MYEditorTimelineTrackView timelineTrackView) {
        this.mTimelineTrackView = timelineTrackView;
    }
}
