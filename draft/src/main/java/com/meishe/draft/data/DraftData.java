package com.meishe.draft.data;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meishe.base.utils.FormatUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.TimeUtils;
import com.meishe.draft.db.LocalDraftEntity;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.UUID;

/**
 * Created by MZ008064 on 2020-05-08.
 * 数据草稿类
 * Data Draft class
 * <AUTHOR>
 * @Date 2020-05-08.
 */
public class DraftData implements Serializable, Cloneable,Comparable<DraftData> {
    /**
     * 草稿时长
     * The draft time
     */
    private String mDuration;

    /**
     * 草稿时长 Long类型
     * The draft time Long
     */
    private long mDurationLong;
    /**
     * 本地最后修改时间
     * Local modification date
     */
    private String mLastModifyTime;

    /**
     * 本地最后修改时间, Long 类型
     * Local modification date Long
     */
    private long mLastModifyTimeLong;

    /**
     * 云端最后修改时间
     * Cloud modification date
     */
    private long mCloudLastModifyTimeLong;

    private long mCreateAt;
    /**
     * 文件夹路径
     * folder path
     */
    private String mDirPath;

    /**
     * 封面信息配置文件路径
     * The cover data config path
     */
    private String mCoverDataConfigPath;
    /**
     * 草稿数据
     * The draft data
     */
    private String mJsonData;
    /**
     * 草稿文件名称
     * Name of draft document
     */
    private String mFileName;
    private String mCoverPath;

    private String projectId;

    private boolean isUpload = false;

    private CloudInfo cloudInfo;

    private Object tag;

    /**
     * 文件大小
     * The file size String
     */
    private String mFileSize;

    /**
     * 文件大小 Long类型
     * The file size Long
     */
    private long mFileSizeLong;

    private boolean mIsCloud;

    private int progress;

    public boolean isUpload() {
        return isUpload;
    }

    public void setUpload(boolean upload) {
        isUpload = upload;
    }

    public String getProjectId() {
        if (TextUtils.isEmpty(projectId)) {
            projectId = UUID.randomUUID().toString().toUpperCase();
        }
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId.toUpperCase();
    }

    public String getDirPath() {
        return mDirPath;
    }

    public void setDirPath(String filePath) {
        this.mDirPath = filePath;
    }

    public String getJsonData() {
        return mJsonData;
    }

    @Deprecated
    public void setJsonData(String jsonData) {
        this.mJsonData = jsonData;
    }

    public String getFileName() {
        return mFileName;
    }

    public void setFileSize(String fileSize) {
        mFileSize = fileSize;
    }

    public String getFileSize() {
        return mFileSize;
    }

    public long getFileSizeLong() {
        return mFileSizeLong;
    }

    public void setFileSizeLong(long fileSizeLong) {
        this.mFileSizeLong = fileSizeLong;
    }

    public void setFileName(String fileName) {
        this.mFileName = fileName;
    }

    public String getDuration() {
        return mDuration;
    }

    public void setDuration(String duration) {
        this.mDuration = duration;
    }

    public String getLastModifyTime() {
        return mLastModifyTime;
    }

    public long getDurationLong() {
        return mDurationLong;
    }

    public void setDurationLong(long durationLong) {
        this.mDurationLong = durationLong;
    }

    public void setLastModifyTime(String lastModifyTime) {
        this.mLastModifyTime = lastModifyTime;
    }

    public void setCoverPath(String coverPath) {
        mCoverPath = coverPath;
    }

    public String getCoverPath() {
        return mCoverPath;
    }

    public long getLastModifyTimeLong() {
        return mLastModifyTimeLong;
    }

    public void setLastModifyTimeLong(long lastModifyTimeLong) {
        this.mLastModifyTimeLong = lastModifyTimeLong;
    }

    public long getCloudLastModifyTimeLong() {
        return mCloudLastModifyTimeLong;
    }

    public void setCloudLastModifyTimeLong(long cloudLastModifyTimeLong) {
        this.mCloudLastModifyTimeLong = cloudLastModifyTimeLong;
    }

    public Object getTag() {
        return tag;
    }

    public void setTag(Object tag) {
        this.tag = tag;
    }

    public long getCreateAt() {
        return mCreateAt;
    }

    public void setCreateAt(long createAt) {
        this.mCreateAt = createAt;
    }

    public boolean isCloud() {
        return mIsCloud;
    }

    public void setIsCloud(boolean isCloud) {
        this.mIsCloud = isCloud;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public String getCoverDataConfigPath() {
        return mCoverDataConfigPath;
    }

    public void setCoverDataConfigPath(String coverDataConfigPath) {
        this.mCoverDataConfigPath = coverDataConfigPath;
    }

    public CloudInfo getCloudInfo() {
        if (cloudInfo == null) {
            cloudInfo = new CloudInfo();
        }
        return cloudInfo;
    }

    public void setCloudInfo(CloudInfo cloudInfo) {
        this.cloudInfo = cloudInfo;
    }

    /**
     * Is only cloud boolean.
     * 是否只有云草稿
     * @return the boolean
     */
    public boolean isOnlyCloud() {
        return TextUtils.isEmpty(getJsonData());

    }

    /**
     * Is modified boolean.
     * 是否进行过编辑
     * @return the boolean
     */
    public boolean isModified(){
        return Math.abs(getCreateAt() - getCloudLastModifyTimeLong()) > 5;
    }

    @Override
    public int compareTo(DraftData o) {
        if (this == o) {
            return 0;
        }
        if (o == null) {
            return -1;
        }
        if (o.getLastModifyTimeLong() > this.getLastModifyTimeLong()) {
            return 1;
        } else if (o.getLastModifyTimeLong() < this.getLastModifyTimeLong()) {
            return -1;
        } else {
            return 0;
        }
    }

    public static class CloudInfo implements Serializable, Cloneable{
        public String uuid;
        public String projectId;
        public String cloudToLocalMapInfo;
        public String infoPath;
        public String templatePath;
        public String templateUrl;
        public String infoUrl;

        @NonNull
        @Override
        public CloudInfo clone() throws CloneNotSupportedException {
            return (CloudInfo) super.clone();
        }
    }

    public static DraftData create(LocalDraftEntity draftEntity) {
        DraftData draftData = new DraftData();
        draftData.setLastModifyTimeLong(draftEntity.getModifiedAt());
        draftData.setCloudLastModifyTimeLong(draftEntity.getCloudModifiedAt());
        draftData.setDirPath(draftEntity.getDirPath());
        draftData.setFileName(draftEntity.getName());
        draftData.setProjectId(draftEntity.getId());
        draftData.setCoverPath(draftEntity.getCoverPath());
        String duration = draftEntity.getDuration();
        try {
            if (!TextUtils.isEmpty(duration)) {
                draftData.setDuration(FormatUtils.microsecond2Time(Long.parseLong(duration)));
                draftData.setDurationLong(Long.parseLong(duration));
            }
        } catch (Exception e) {}
        try {
            draftData.setFileSizeLong(Long.parseLong(draftEntity.getFileSize()));
        } catch (Exception e) {}
        String currentTime = TimeUtils.millis2String(draftData.getLastModifyTimeLong(), new SimpleDateFormat("yyyy.MM.dd HH:mm"));
        draftData.setLastModifyTime(currentTime);
        draftData.setIsCloud(draftEntity.isCloud());
        draftData.setCreateAt(draftEntity.getCreateAt());

        CloudInfo cloudInfo = new CloudInfo();
        cloudInfo.infoPath = draftEntity.getInfoJsonPath();
        cloudInfo.projectId = draftEntity.getRemoteId();
        cloudInfo.cloudToLocalMapInfo = draftEntity.getCloudToLocalMapInfo();
        cloudInfo.templatePath = draftEntity.getTemplatePath();
        draftData.setCloudInfo(cloudInfo);

        return draftData;
    }

    public static LocalDraftEntity createLocalEntity(DraftData data) {
        LocalDraftEntity entity = new LocalDraftEntity(data.getProjectId());
        entity.setName(data.getFileName());
        entity.setId(data.getProjectId());
        entity.setModifiedAt(data.getLastModifyTimeLong());
        entity.setCloudModifiedAt(data.getCloudLastModifyTimeLong());
        entity.setDuration(data.getDuration());
        entity.setCoverPath(data.getCoverPath());
        entity.setDirPath(data.getDirPath());
        entity.setFileSize(String.valueOf(data.getFileSizeLong()));
        entity.setCreateAt(entity.getModifiedAt());
        return entity;
    }


    @NonNull
    @Override
    public DraftData clone() {
        try {
            DraftData clone = (DraftData) super.clone();
            clone.setCloudInfo(cloudInfo.clone());
            return clone;
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return null;
    }
}
