<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <RelativeLayout
        android:id="@+id/fragment_base_parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true">

        <ImageView
            android:id="@+id/fragment_base_live_window"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true" />
    </RelativeLayout>
    <com.meishe.player.view.RectSelectView
        app:innerPadding = "@dimen/dp_px_87"
        android:id="@+id/rect_selector"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
    <com.meishe.player.view.TestRectView
        android:id="@+id/test"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </com.meishe.player.view.TestRectView>

    <TextView
        android:layout_marginTop="@dimen/dp_px_50"
        android:layout_centerHorizontal="true"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_33"
        android:textColor="@color/color_ffb1b1b1"
        android:text="@string/activity_tailor_rect_select_hint"/>
</RelativeLayout>