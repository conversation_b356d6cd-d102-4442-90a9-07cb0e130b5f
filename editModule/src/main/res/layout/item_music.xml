<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_225">


    <ImageView
        android:id="@+id/iv_music"
        android:layout_width="@dimen/dp_px_150"
        android:layout_height="@dimen/dp_px_150"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp_px_40"
        android:layout_marginLeft="@dimen/dp_px_40"
        android:background="@mipmap/ic_music_single_btn"
        android:contentDescription="@null" />

    <TextView
        android:id="@+id/tv_music_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/iv_music"
        android:layout_marginStart="@dimen/dp_px_24"
        android:layout_marginLeft="@dimen/dp_px_24"
        android:layout_marginTop="@dimen/dp_px_15"
        android:layout_toStartOf="@+id/ibt_play"
        android:layout_toLeftOf="@+id/ibt_play"
        android:layout_toEndOf="@+id/iv_music"
        android:layout_toRightOf="@+id/iv_music"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        android:id="@+id/tv_music_author"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_music_name"
        android:layout_marginStart="@dimen/dp_px_24"
        android:layout_marginLeft="@dimen/dp_px_24"
        android:layout_marginTop="@dimen/dp_px_15"
        android:layout_toStartOf="@+id/ibt_play"
        android:layout_toLeftOf="@+id/ibt_play"
        android:layout_toEndOf="@+id/iv_music"
        android:layout_toRightOf="@+id/iv_music"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@color/white_909"
        android:textSize="@dimen/sp_px_36" />

    <ImageButton
        android:id="@+id/ibt_play"
        android:layout_width="@dimen/dp_px_108"
        android:layout_height="@dimen/dp_px_108"
        android:layout_alignParentEnd="true"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_40"
        android:layout_marginRight="@dimen/dp_px_40"
        android:background="@mipmap/ic_music_play"
        android:contentDescription="@null" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_2"
        android:layout_alignParentBottom="true"
        android:background="@color/white_07" />
</RelativeLayout>
