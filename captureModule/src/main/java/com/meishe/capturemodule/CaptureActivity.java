package com.meishe.capturemodule;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.hardware.SensorManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.OrientationEventListener;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.activity.result.ActivityResult;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityOptionsCompat;
import androidx.fragment.app.Fragment;

import com.meicam.sdk.NvsCaptureVideoFx;
import com.meicam.sdk.NvsLiveWindowExt;
import com.meicam.sdk.NvsStreamingContext;
import com.meicam.sdk.NvsTimeline;
import com.meicam.sdk.NvsVideoFrameRetriever;
import com.meishe.base.bean.MediaData;
import com.meishe.base.constants.Constants;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.FileUtils;
import com.meishe.base.utils.ImageUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.ScreenUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.base.utils.Utils;
import com.meishe.business.assets.view.MYMultiBottomView;
import com.meishe.capturemodule.bean.BeautyShapeDataItem;
import com.meishe.capturemodule.bean.CaptureFxModel;
import com.meishe.capturemodule.dialog.CaptureBeautyDialog;
import com.meishe.capturemodule.dialog.CaptureExitDialog;
import com.meishe.capturemodule.dialog.ItemListDialog;
import com.meishe.capturemodule.iview.ICaptureView;
import com.meishe.capturemodule.makeup.MakeupManager;
import com.meishe.capturemodule.presenter.CapturePresenter;
import com.meishe.capturemodule.presenter.MultiBottomHelper;
import com.meishe.capturemodule.utils.CaptureToastUtil;
import com.meishe.capturemodule.utils.PathUtils;
import com.meishe.capturemodule.utils.TimeFormatUtil;
import com.meishe.capturemodule.view.CaptureMusicControlView;
import com.meishe.capturemodule.view.ExposureSeekBarView;
import com.meishe.capturemodule.view.MagicProgress;
import com.meishe.capturemodule.view.NvsLiveWindowWrapper;
import com.meishe.capturemodule.view.TimeDownView;
import com.meishe.capturemodule.view.fragment.CaptureEffectFragment;
import com.meishe.engine.bean.BaseInfo;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.engine.observer.EngineCallbackObserver;
import com.meishe.myvideo.activity.SelectMusicActivity;
import com.meishe.myvideo.bean.MusicInfo;
import com.meishe.myvideo.util.AudioPlayer;
import com.meishe.third.pop.core.BasePopupView;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;

import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_EFFECT;
import static com.meishe.business.assets.view.MYMultiBottomView.TYPE_MENU_PROP;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_DATA;



/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * <AUTHOR> LiuPanFeng
 * @CreateDate : 2021/2/7 9:58
 * @Description :拍摄页面 the capture activity
 * @Copyright : www.meishesdk.com Inc. All rights reserved.
 */
public class CaptureActivity extends BaseMvpActivity<CapturePresenter> implements ICaptureView, NvsStreamingContext.CaptureDeviceCallback, NvsStreamingContext.CaptureRecordingDurationCallback, NvsStreamingContext.CaptureRecordingStartedCallback, NvsStreamingContext.CaptureRecordingFrameReachedCallback{

    private static final String TAG = CaptureActivity.class.getSimpleName();
    /**
     * The min section for rotation
     * 最小旋转区间
     */
    private static final int SECTION_MIN_ROTATION = 20;
    /**
     * The degree of half PI
     * 90度
     */
    private static final int DEGREE_HALF_PI = 90;
    /**
     * The degree of PI
     * 180度
     */
    private static final int DEGREE_PI = 180;

    private int mViewRotation;

    private NvsLiveWindowWrapper mLiveWindow;
    private ImageView mIvExit, mIvMore, mIvChangeCamera, mIvRatio;

    private LinearLayout mBeautyLayout;
    private LinearLayout mFilterLayout;
    private LinearLayout mFuLayout;
    private FrameLayout mFlStartRecord;
    private TextView mStartText;
    private ImageView mDelete;
    private ImageView mNext;
    private TextView mRecordTime;
    /**
     * 拍照or视频
     * Photo or video
     */
    private LinearLayout mRecordTypeLayout;
    private FrameLayout mFlMiddleParent;
    private TextView mTvChoosePicture, mTvChooseVideo;
    private ImageView mIvTakePhotoBg;
    private int mRecordType = CaptureConstants.RECORD_TYPE_VIDEO;

    /**
     * 录制
     * Record
     */
    private final ArrayList<Long> mRecordTimeList = new ArrayList<>();
    private final ArrayList<String> mRecordFileList = new ArrayList<>();
    private long mEachRecodingVideoTime = 0;
    private final long mEachRecodingImageTime = 4000000;
    private long mAllRecordingTime = 0;
    private String mCurRecordVideoPath;
    private int mCurrentDeviceIndex = 1;
    private boolean mIsSwitchingCamera;

    /**
     * 美颜Dialog
     * Beauty Dialog
     */
    private CaptureBeautyDialog mCaptureBeautyDialog;

    /**
     * 是否初始化完成
     * Whether initialization is complete
     */
    private boolean initArScene = true;

    private NvsCaptureVideoFx mCurCaptureVideoFx;
    /**
     * 拍摄比例
     * The record ratio
     */
    private float mRecordRatio = CommonData.AspectRatio.ASPECT_9V16.getRatio();

    private float mRealRecordRatio = CommonData.AspectRatio.ASPECT_9V16.getRatio();
    private int mReCordItem = 3;
    private ItemListDialog mRatioDialog;
    private int mDelayTime;
    private ItemListDialog mMoreOptionDialog;
    private TimeDownView mTimeDownView;
    private View mTimerLayout;
    private TimeDownView.DownTimeWatcher mDownTimeWatcher;
    private BasePopupView mExitPop;
    private ExposureSeekBarView mAutoFocusExposureView;
    private MultiBottomHelper mMultiBottomHelper;
    /**
     * 变焦 the TextView for zoom
     */
    private TextView tvZoom;
    private MYMultiBottomView mMultiBottomView;
    private SensorManager mSensorManager;
    /**
     * 第一次按删除按钮 Whether the first delete is clicked.
     */
    private boolean isFirstDelete = true;
    /**
     * 滤镜滑杆布局
     * The filter layout
     */
    private LinearLayout mLinearFilter;
    private MagicProgress mSbFilter;
    /**
     * 双指缩放所有的缩放值
     * The all progress of pinch-to-zoom
     */
    private float allProgress = 0;
    private float mCurrentRatio = -1;
    private View mDelayDecs, mBeautyDecs, mFilterDecs;
    private CaptureMusicControlView mMusicControlView;
    private ActivityResultLauncher<Intent> intentActivityResultLauncher;
    /**
     * 当前选中的音乐
     * The currently selected music
     */
    private MusicInfo mSelectMusicInfo;
    private int mMusicStartIndex;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        Utils.hideStatusBar(this);
        super.onCreate(savedInstanceState);
        startOrientationChangeListener();
    }

    private void showPropsToast(String sceneId) {
        mPresenter.showPropsToast(sceneId);
    }

    private void initBeautyRecyclerView() {
        if (mCaptureBeautyDialog == null) {
            mCaptureBeautyDialog = new CaptureBeautyDialog(this, mPresenter.getShapeDataList());
            mCaptureBeautyDialog.setEventListener(new CaptureBeautyDialog.EventListener() {
                @Override
                public void onDismiss() {
                }

                @Override
                public void onCancel() {
                }

                @Override
                public void onReset() {
                    for (BeautyShapeDataItem beautyShapeDataItem : mPresenter.getShapeDataList()) {
                        beautyShapeDataItem.strength = beautyShapeDataItem.defaultStrength;
                        mPresenter.applyBeautyData(beautyShapeDataItem);
                    }
                    BeautyShapeDataItem selectItem = mCaptureBeautyDialog.getSelectItem();
                    if (selectItem != null) {
                        updateSeekBar(selectItem);
                    }
                }

                @Override
                public void onBeautyChecked(boolean isOpen) {
                    setBeautySwitchChecked(isOpen);
                    CaptureToastUtil.showCaptureToast(isOpen ? getResources().getString(R.string.beauty_open) :
                            getResources().getString(R.string.beauty_close));
                }

                @Override
                public void onProgressChange(int progress, boolean fromUser) {
                    BeautyShapeDataItem selectItem = mCaptureBeautyDialog.getSelectItem();
                    if (selectItem == null) {
                        Log.e(TAG, "onProgressChange selectItem is null");
                        return;
                    }
                    int effectType = selectItem.effectType;
                    if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHAPE) {
                        selectItem.strength = ((float) (progress - 100) / 100);
                    } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE && selectItem.isShape) {
                        selectItem.strength = ((float) (100 - progress) / 100);
                    } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHARPNESS) {
                        //取值范围 0 - 1.5；Value range: 0-1.5;
                        selectItem.strength = 1.5 * (progress * 1.0 / 100);
                    } else {
                        selectItem.strength = progress * 1.0 / 100;
                    }
                    mPresenter.applyBeautyData(selectItem);
                }

                @Override
                public void onItemClick(View view, int position) {
                    BeautyShapeDataItem selectItem = mPresenter.getShapeDataList().get(position);
                    updateSeekBar(selectItem);
                    mPresenter.applyBeautyData(selectItem);
                }
            });
        }
        mCaptureBeautyDialog.showCaptureDialogView();

    }

    private void updateSeekBar(BeautyShapeDataItem selectItem) {
        double defaultLevel = selectItem.defaultStrength;
        double level = selectItem.strength;
        int effectType = selectItem.effectType;
        if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SMALL_SHAPE && selectItem.isShape) {
            mCaptureBeautyDialog.setMax(200);
            mCaptureBeautyDialog.setBreakProgress(100);
            if (level >= 0) {
                level = (Math.round(level * 100)) * 0.01;
            } else {
                level = -Math.round((Math.abs(level) * 100)) * 0.01;
            }
            if (defaultLevel >= 0) {
                defaultLevel = (Math.round(defaultLevel * 100)) * 0.01;
            } else {
                defaultLevel = -Math.round((Math.abs(defaultLevel) * 100)) * 0.01;
            }
            mCaptureBeautyDialog.setPointProgress((int) (defaultLevel * 100 + 100));
            mCaptureBeautyDialog.setProgress((int) (level * 100 + 100));
        } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHAPE) {
            mPresenter.setMarkUpSelected(true);
            mCaptureBeautyDialog.setMax(200);
            mCaptureBeautyDialog.setBreakProgress(100);
            if (level >= 0) {
                level = (Math.round(level * 100)) * 0.01;
            } else {
                level = -Math.round((Math.abs(level) * 100)) * 0.01;
            }
            mCaptureBeautyDialog.setProgress((int) (level * 100 + 100));
            mCaptureBeautyDialog.setPointProgress((int) (defaultLevel * 100 + 100));

        } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHARPNESS) {
            mCaptureBeautyDialog.setMax(100);
            mCaptureBeautyDialog.setBreakProgress(0);
            mCaptureBeautyDialog.setPointProgress((int) (defaultLevel * 100 / 1.5));
            mCaptureBeautyDialog.setProgress((int) (level * 100 / 1.5));
        } else {
            mCaptureBeautyDialog.setMax(100);
            mCaptureBeautyDialog.setBreakProgress(0);
            mCaptureBeautyDialog.setPointProgress((int) (defaultLevel * 100));
            mCaptureBeautyDialog.setProgress((int) (level * 100));
        }
    }


    private void startOrientationChangeListener() {
        if (mSensorManager == null) {
            mSensorManager = (SensorManager) getSystemService(SENSOR_SERVICE);
        }
        OrientationEventListener mOrientationListener = new OrientationEventListener(this) {
            @Override
            public void onOrientationChanged(int rotation) {
                if (rotation == OrientationEventListener.ORIENTATION_UNKNOWN) {
                    // 手机平放时，检测不到有效的角度
                    // When the phone is placed flat, no valid angle can be detected.
                    return;
                }
                if (mPresenter.isRecording()) {
                    return;
                }
                rotateViews(getRotationDegree(rotation),
                        mAutoFocusExposureView, mBeautyLayout, mFilterLayout,
                        mFuLayout, mIvExit, mIvTakePhotoBg, mIvMore,
                        mIvRatio, mIvChangeCamera, tvZoom);
            }
        };
        mOrientationListener.enable();
    }

    private int getRotationDegree(int rotation) {
        float orientation = Math.abs(rotation % DEGREE_HALF_PI);
        if (orientation <= SECTION_MIN_ROTATION) {
            if (Math.abs(rotation % DEGREE_PI) <= SECTION_MIN_ROTATION) {
                return rotation > DEGREE_HALF_PI ? 180 : 0;
            } else {
                return rotation > DEGREE_PI ? 90 : -90;
            }
        }
        return -1;
    }

    private void rotateViews(int rotation, View... view) {
        if (rotation == -1) {
            return;
        }
        if (rotation == mViewRotation) {
            return;
        }
        mViewRotation = rotation;
        if (mRecordRatio > 1) {
            mRealRecordRatio = 1F / mRealRecordRatio;
            startCapturePreview(true, mRealRecordRatio);
        }
        initAutoFocusExposureViewLayout(mRealRecordRatio, rotation);
        if (view.length > 0) {
            AnimatorSet animatorSet = new AnimatorSet();
            ObjectAnimator[] animators = new ObjectAnimator[view.length];
            for (int i = 0; i < view.length; i++) {
                animators[i] = ObjectAnimator.ofFloat(view[i],
                        "rotation", view[i].getRotation(), rotation);
            }
            animatorSet.setDuration(200);
            animatorSet.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    if(mMultiBottomHelper.isShow()){
                        return;
                    }
                    mAutoFocusExposureView.setTouchAble(true);
                }

                @Override
                public void onAnimationCancel(Animator animation) {

                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animatorSet.playTogether(animators);
            animatorSet.start();
        }
    }

    private void setBeautySwitchChecked(final boolean isOpen) {
        mPresenter.setBeautySwitchChecked(isOpen);
        if (isOpen) {
            for (BeautyShapeDataItem beautyShapeDataItem : mPresenter.getShapeDataList()) {
                if (beautyShapeDataItem.effectType == BeautyShapeDataItem.EFFECT_TYPE_MAKEUP) {
                    //如果是美妆，且没有添加过，则不再应用
                    // If it is a beauty product and has not been added before, it will no longer be applied.
                    if (!mPresenter.isMarkUpSelected()) {
                        continue;
                    }
                }
                mPresenter.applyBeautyData(beautyShapeDataItem);
            }
        } else {
            for (BeautyShapeDataItem beautyShapeDataItem : mPresenter.getShapeDataList()) {
                int effectType = beautyShapeDataItem.effectType;
                if (effectType == BeautyShapeDataItem.EFFECT_TYPE_MAKEUP) {
                    mPresenter.clearMakeUp();
                } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_DEFINITION) {
                    mPresenter.setDefinitionFloatVal(beautyShapeDataItem.beautyShapeId, 0);
                } else if (effectType == BeautyShapeDataItem.EFFECT_TYPE_SHARPNESS) {
                    mPresenter.setSharpenFloatVal(beautyShapeDataItem.beautyShapeId, 0);
                } else {
                    mPresenter.setARSceneFloatVal(beautyShapeDataItem.beautyShapeId, 0);
                }
            }
        }
    }


    @SuppressLint("ClickableViewAccessibility")
    protected void initListener() {
        /*
         *给Streaming Context设置回调接口
         *Set callback interface for Streaming Context
         * */
        setStreamingCallback(false);
        mMultiBottomView.setMultiBottomEventListener(new MYMultiBottomView.MultiBottomEventListener() {
            @Override
            public void onEditTextChange(String text) {

            }

            @Override
            public void onConfirm(int type) {

            }

            @Override
            public void onApplyToAll(Fragment fragment) {

            }

            @Override
            public void onErasure(int type) {
                Fragment selectedFragment = mMultiBottomView.getSelectedFragment();
                if (selectedFragment instanceof CaptureEffectFragment) {
                    CaptureEffectFragment captureEffectFragment = (CaptureEffectFragment) selectedFragment;
                    captureEffectFragment.setSelected(-1);
                }
                if (type == TYPE_MENU_EFFECT) {
                    mLinearFilter.setVisibility(View.GONE);
                    mPresenter.removeAllFilterFx();
                }
                if (type == TYPE_MENU_PROP) {
                    mPresenter.setARSceneStringVal("Scene Id", "");
                }
            }

            @Override
            public void onFragmentSelected(Fragment fragment, int type) {
                CaptureEffectFragment captureEffectFragment = null;
                if (fragment instanceof CaptureEffectFragment) {
                    captureEffectFragment = (CaptureEffectFragment) fragment;
                }
                if (captureEffectFragment == null) {
                    return;
                }
                if (type == TYPE_MENU_EFFECT) {
                    captureEffectFragment.setSelected(
                            mPresenter.getFxInfo(CapturePresenter.FX_TYPE_FILTER).getFxId());
                }
                if (type == TYPE_MENU_PROP) {
                    captureEffectFragment.setSelected(
                            mPresenter.getFxInfo(CapturePresenter.FX_TYPE_PROP).getFxId());
                }
            }
        });
        mSbFilter.setOnProgressChangeListener(new MagicProgress.OnProgressChangeListener() {
            @Override
            public void onProgressChange(int progress, boolean fromUser) {
                float intensity = progress / 100.0f;
                if (mCurCaptureVideoFx != null) {
                    mCurCaptureVideoFx.setFilterIntensity(intensity);
                }
            }
        });
        mLiveWindow.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mMultiBottomHelper.hide();

            }
        });
        mAutoFocusExposureView.setOnSeekBarChangedListener(new ExposureSeekBarView.OnSeekBarChangedListener() {


            @Override
            public void onSeekBarChanged(float progress) {
                mPresenter.setExposureCompensation(progress);
            }

            @Override
            public void onTouchDown(RectF rectF) {
                mPresenter.startAutoFocus(mAutoFocusExposureView.getExposureRect());
                mPresenter.setExposureCompensation(0);
            }

            @Override
            public void onTowFingerMoved(float scale) {
                mPresenter.setQuickZoomTimes(0);
                setZoom((int) (mPresenter.getSupportMaxZoom() * (allProgress + scale)));
            }

            @Override
            public void onTowFingerUp() {
                allProgress = mPresenter.getZoom() * 1F / mPresenter.getSupportMaxZoom();
            }
        });

        mIvExit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!hasRecord()) {
                    finish();
                    return;
                }
                if (mExitPop == null) {
                    int offX = SizeUtils.dp2px(23);
                    int offY = SizeUtils.dp2px(93);
                    mExitPop = CaptureExitDialog.create(CaptureActivity.this, offX, offY, new CaptureExitDialog.OnItemClickListener() {
                        @Override
                        public void toShoot() {
                            ThreadUtils.getIoPool().execute(new Runnable() {
                                @Override
                                public void run() {
                                    //删除录制的文件,没有保存过的
                                    // Delete recorded files that have not been saved.
                                    for (String filePath : mRecordFileList) {
                                        FileUtils.delete(filePath);
                                    }
                                    mRecordFileList.clear();
                                }
                            });
                            mRecordTimeList.clear();
                            mAllRecordingTime = 0;
                            mStartText.setText("");
                            changeRecordDisplay(RECORD_DEFAULT, mRecordType == Constants.RECORD_TYPE_PICTURE);
                        }

                        @Override
                        public void exit() {
                            finish();
                        }
                    });
                }
                mExitPop.show();
            }
        });
        /*
         * 切换摄像头开关
         * Toggle camera switch
         */
        mIvChangeCamera.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mIsSwitchingCamera) {
                    return;
                }
                if (mCurrentDeviceIndex == 0) {
                    mCurrentDeviceIndex = 1;
                } else {
                    mCurrentDeviceIndex = 0;
                }
                mIsSwitchingCamera = true;
                //切换摄像头的时候缩放要重置
                // When switching cameras, the zoom needs to be reset.
                resetZoom();
                tryStartCapturePreview(true, mRecordRatio);
            }
        });
        /*
         * 变焦
         * zoom
         */
        tvZoom.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                setZoom(mPresenter.getQuickZoom());
                allProgress = mPresenter.getZoom() * 1F / mPresenter.getSupportMaxZoom();
            }
        });
        mIvMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ItemListDialog.Builder builder = new ItemListDialog.Builder();
                if (mMoreOptionDialog == null) {
                    mMoreOptionDialog = builder.setItemHeight(SizeUtils.dp2px(30))
                            .setLayoutHeight(SizeUtils.dp2px(45))
                            .setLayoutWidth(SizeUtils.dp2px(220))
                            .setOffY(SizeUtils.dp2px(93))
                            .build(CaptureActivity.this, mPresenter.getMoreData(), new ItemListDialog.OnItemClickListener() {
                                @Override
                                public void onItemClicked(int position, ItemListDialog.ItemInfo itemInfo) {
                                    switch (position) {
                                        case 0:
                                            mDelayTime = (int) itemInfo.tagList[itemInfo.currentIconIndex];
                                            break;
                                        case 1:
                                            mPresenter.changeFlash();
                                            break;
                                        case 2:
                                            mPresenter.setCaptureResolutionGrade(
                                                    (int) itemInfo.tagList[itemInfo.currentIconIndex]);
                                            tryStartCapturePreview(true, mRecordRatio);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            });
                }
                mMoreOptionDialog.setFrontCamera(mCurrentDeviceIndex == 1);
                mMoreOptionDialog.show();
            }
        });

        mIvRatio.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ItemListDialog.Builder builder = new ItemListDialog.Builder();
                if (mRatioDialog == null) {
                    mRatioDialog = builder.setItemHeight(SizeUtils.dp2px(25))
                            .setLayoutHeight(SizeUtils.dp2px(65))
                            .setLayoutWidth(SizeUtils.dp2px(300)).setCurrentItem(mReCordItem)
                            .setOffY(SizeUtils.dp2px(93))
                            .build(CaptureActivity.this, mPresenter.getResolutionItemList(), new ItemListDialog.OnItemClickListener() {
                                @Override
                                public void onItemClicked(int position, ItemListDialog.ItemInfo itemInfo) {
                                    mReCordItem = position;
                                    mRecordRatio = (Float) itemInfo.tagList[itemInfo.currentIconIndex];
                                    int rotation = (int) mAutoFocusExposureView.getRotation();
                                    if (rotation % DEGREE_PI != 0 && mRecordRatio > 1) {
                                        mRealRecordRatio = 1F / mRecordRatio;
                                    } else {
                                        mRealRecordRatio = mRecordRatio;
                                    }
                                    startCapturePreview(true, mRealRecordRatio);
                                    initAutoFocusExposureViewLayout(mRealRecordRatio, rotation);
                                    mIvRatio.setImageResource(itemInfo.normalIcon);
                                }
                            });
                }
                mRatioDialog.setCurrentItem(mReCordItem);
                mRatioDialog.show();
            }
        });
        /*
         * 美颜
         * Beauty
         */
        mBeautyLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                initBeautyRecyclerView();
            }
        });

        mMusicControlView.setOnEventChangedListener(new CaptureMusicControlView.OnEventChangedListener() {
            @Override
            public void onSelectClicked() {
                selectMusic();
            }

            @Override
            public boolean onDeleteClicked() {
                return deleteMusic();
            }
        });


        /*
         * 滤镜
         * Filter
         */
        mFilterLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAutoFocusExposureView.setTouchAble(false);
                mMultiBottomHelper.showEffectView(mPresenter.getFxInfo(CapturePresenter.FX_TYPE_FILTER).getFxId(),
                        new CaptureEffectFragment.OnStateChangedListener() {
                            @Override
                            public void onItemClicked(IBaseInfo baseInfo) {
                                mLinearFilter.setVisibility(View.VISIBLE);
                                mPresenter.removeAllFilterFx();
                                CaptureFxModel.FxInfo fxInfo = mPresenter.getFxInfo(CapturePresenter.FX_TYPE_FILTER);
                                int filterMode = baseInfo.getEffectMode();
                                if (filterMode == BaseInfo.EFFECT_MODE_BUILTIN) {
                                    String filterName = baseInfo.getEffectId();
                                    if (!TextUtils.isEmpty(filterName)) {
                                        mCurCaptureVideoFx = mPresenter.appendBuiltinCaptureVideoFx(filterName);
                                        fxInfo.setFxMode(CaptureFxModel.FXMODE_BUILTIN);
                                        fxInfo.setFxId(filterName);
                                    }
                                } else {
                                    String filterPackageId = baseInfo.getPackageId();
                                    if (!TextUtils.isEmpty(filterPackageId)) {
                                        mCurCaptureVideoFx = mPresenter.appendPackagedCaptureVideoFx(filterPackageId);
                                        fxInfo.setFxMode(CaptureFxModel.FXMODE_PACKAGE);
                                        fxInfo.setFxId(filterPackageId);
                                    }
                                }
                                if (mCurCaptureVideoFx == null) {
                                    LogUtils.e("mFilterLayout showEffectView 滤镜对象创建失败");
                                    return;
                                }
                                mCurCaptureVideoFx.setFilterIntensity(mSbFilter.getProgress() / 100.0f);
                            }

                            @Override
                            public void onDismiss() {
                                mLinearFilter.setVisibility(View.GONE);
                                mAutoFocusExposureView.setTouchAble(true);
                            }
                        });

            }
        });
        /*
         * 道具
         * Props
         */
        mFuLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                /*
                 * 只有美摄道具才可以使用
                 *  Only beauty photo props can be used
                 */
                mAutoFocusExposureView.setTouchAble(false);
                mMultiBottomHelper.showPropView(mPresenter.getFxInfo(CapturePresenter.FX_TYPE_PROP).getFxId(),
                        new CaptureEffectFragment.OnStateChangedListener() {
                            @Override
                            public void onItemClicked(IBaseInfo baseInfo) {
                                String sceneId = baseInfo.getPackageId();
                                showPropsToast(sceneId);
                                mPresenter.setARSceneStringVal("Scene Id", sceneId);
                                CaptureFxModel.FxInfo fxInfo = mPresenter.getFxInfo(CapturePresenter.FX_TYPE_PROP);
                                fxInfo.setFxId(sceneId);
                                fxInfo.setFxMode(CaptureFxModel.FXMODE_PACKAGE);
                            }

                            @Override
                            public void onDismiss() {
                                mAutoFocusExposureView.setTouchAble(true);
                            }
                        });


            }
        });
        /*
         * 开始录制
         *Start recording
         */
        mFlStartRecord.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                isFirstDelete = true;
                mDelete.setImageResource(R.mipmap.capture_back_delete);
                mAutoFocusExposureView.setTouchAble(false);
                /*
                 * 当前在录制状态，可停止视频录制
                 * Currently in recording state, you can stop video recording
                 */
                if (mPresenter.isRecording()) {
                    stopRecording();
                } else {
                    if (mDelayTime > 0) {
                        mFlStartRecord.setVisibility(View.GONE);
                        mRecordTime.setVisibility(View.GONE);
                        changeRecordDisplay(RECORDING, false);
                        if (mTimerLayout.getVisibility() == View.VISIBLE) {
                            return;
                        }
                        mTimerLayout.setVisibility(View.VISIBLE);
                        mTimeDownView.setVisibility(View.VISIBLE);
                        mTimeDownView.downSecond(mDelayTime);
                        if (mDownTimeWatcher == null) {
                            mDownTimeWatcher = new TimeDownView.DownTimeWatcher() {
                                @Override
                                public void onTime(int num) {

                                }

                                @Override
                                public void onLastTime(int num) {

                                }

                                @Override
                                public void onLastTimeFinish(int num) {
                                    mFlStartRecord.setVisibility(View.VISIBLE);
                                    mRecordTime.setVisibility(View.VISIBLE);
                                    mTimerLayout.setVisibility(View.GONE);
                                    mTimeDownView.setVisibility(View.GONE);
                                    startRecord();
                                }
                            };
                        }
                        mTimeDownView.setOnTimeDownListener(mDownTimeWatcher);
                    } else {
                        startRecord();
                    }
                }
            }
        });

        /*
         * 删除视频
         * Delete video
         */
        mDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (isFirstDelete) {
                    isFirstDelete = false;
                    mDelete.setImageResource(R.mipmap.capture_back_delete_selected);
                    return;
                }
                isFirstDelete = true;
                mDelete.setImageResource(R.mipmap.capture_back_delete);
                if (mRecordTimeList.size() != 0 && mRecordFileList.size() != 0) {
                    mAllRecordingTime -= mRecordTimeList.get(mRecordTimeList.size() - 1);
                    mRecordTimeList.remove(mRecordTimeList.size() - 1);
                    PathUtils.deleteFile(mRecordFileList.get(mRecordFileList.size() - 1));
                    mRecordFileList.remove(mRecordFileList.size() - 1);
                    mRecordTime.setText(TimeFormatUtil.formatUsToString2(mAllRecordingTime));

                    if (mRecordTimeList.size() == 0) {
                        changeRecordDisplay(RECORD_DEFAULT, mRecordType == Constants.RECORD_TYPE_PICTURE);
                    } else {
                        mStartText.setText(mRecordTimeList.size() + "");
                        mRecordTime.setVisibility(View.VISIBLE);
                    }

                    if (needHideMusicSelectBar()) {
                        mMusicControlView.setVisibility(View.GONE);
                    } else {
                        mMusicControlView.setVisibility(View.VISIBLE);
                    }
                    if (mMusicStartIndex > mRecordTimeList.size()) {
                        mMusicStartIndex = mRecordTimeList.size();
                    }
                }

            }
        });
        /*
         * 下一步，进入编辑
         * Next, enter edit
         */
        mNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (Utils.isFastClick()) {
                    return;
                }
                ArrayList<MediaData> mediaDataArrayList = mPresenter.getMediaData(mRecordFileList);
                if (mediaDataArrayList == null) {
                    return;
                }
                long musicStartPoint = 0;
                MusicInfo.SampleMusicInfo info;
                if (mMusicStartIndex >= mRecordTimeList.size()) {
                    info = null;
                } else {
                    info = mSelectMusicInfo !=  null? mSelectMusicInfo.createSampleInfo() : null;
                    for (int i = 0; i < mMusicStartIndex; i++) {
                        musicStartPoint += mRecordTimeList.get(i);
                    }
                }
                CapturePreviewActivity.start(CaptureActivity.this, mediaDataArrayList, info, musicStartPoint);
            }
        });

        mTvChoosePicture.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                selectRecordType(true);
            }
        });
        mTvChooseVideo.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                selectRecordType(false);
            }
        });

    }

    private boolean needHideMusicSelectBar(){
        return (mMusicStartIndex < mRecordFileList.size() && mSelectMusicInfo != null);
    }

    /**
     * 重置缩放
     * Reset zoom.
     */
    public void resetZoom() {
        mPresenter.setQuickZoomTimes(0);
        allProgress = 0;
        tvZoom.setText(String.format("%s%s", 1, getString(R.string.zoom)));
    }

    /**
     * 设置变焦
     * Sets zoom.
     *
     * @param zoom the zoom
     */
    private void setZoom(int zoom) {
        if (zoom < 0) {
            return;
        }
        if (mPresenter.isSupportZoom()) {
            if (zoom > mPresenter.getSupportMaxZoom()) {
                CaptureToastUtil.showCaptureToast(getResources().getString(R.string.toast_message_support_max));
            } else {
                mPresenter.setZoom(zoom);
                float displayZoom = zoom / 10.0f + 1;
                tvZoom.setText(String.format("%s%s", displayZoom, getString(R.string.zoom)));
            }
        } else {
            CaptureToastUtil.showCaptureToast(getResources().getString(R.string.toast_message_support));
        }
    }

    private void startRecord() {
        mCurrRecordStatus = RECORDING;
        mCurRecordVideoPath = PathUtils.getRecordVideoPath();
        if (mCurRecordVideoPath == null) {
            return;
        }
        mFlStartRecord.setEnabled(false);
        if (mRecordType == Constants.RECORD_TYPE_VIDEO) {
            mEachRecodingVideoTime = 0;
            /*
             * 当前未在视频录制状态，则启动视频录制。此处使用带特效的录制方式
             * If video recording is not currently in progress, start video recording. Use the recording method with special effects here
             */
            int flag = mSelectMusicInfo != null ? NvsStreamingContext.STREAMING_ENGINE_RECORDING_FLAG_ONLY_RECORD_VIDEO : 0;
            if (!mPresenter.startRecording(mCurRecordVideoPath, flag)) {
                return;
            }
            if (mSelectMusicInfo != null) {
                AudioPlayer.getInstance().startPlay(getPlayPosition());
            }
            changeRecordDisplay(RECORDING, false);
            mRecordFileList.add(mCurRecordVideoPath);
        } else if (mRecordType == Constants.RECORD_TYPE_PICTURE) {
            int flag = mSelectMusicInfo != null ? NvsStreamingContext.STREAMING_ENGINE_RECORDING_FLAG_ONLY_RECORD_VIDEO : 0;
            mPresenter.startRecording(mCurRecordVideoPath, flag);
        }
    }


    private void changeCaptureDisplay(boolean display) {
        if (display) {
            if (!mRecordTimeList.isEmpty()) {
                mFlMiddleParent.setVisibility(View.VISIBLE);
            }
            mIvExit.setVisibility(View.VISIBLE);
            mIvMore.setVisibility(View.VISIBLE);
            mIvChangeCamera.setVisibility(View.VISIBLE);
            mIvRatio.setVisibility(View.VISIBLE);
        } else {
            mIvMore.setVisibility(View.INVISIBLE);
            mIvChangeCamera.setVisibility(View.INVISIBLE);
            mIvRatio.setVisibility(View.INVISIBLE);
            mFlMiddleParent.setVisibility(View.INVISIBLE);
        }
    }


    private void stopRecording() {
        mCurrRecordStatus = RECORD_FINISH;
        mPresenter.stopRecording();
        if (mSelectMusicInfo != null) {
            AudioPlayer.getInstance().stopPlay();
        }
        // mStartRecordingImage.setBackgroundResource(R.mipmap.capture_recording_stop);
        /*
         * 拍视频
         * Take a video
         * */
        if (mRecordType == Constants.RECORD_TYPE_VIDEO) {
            mAllRecordingTime += mEachRecodingVideoTime;
            mRecordTimeList.add(mEachRecodingVideoTime);
            mStartText.setText(mRecordTimeList.size() + "");
            changeRecordDisplay(RECORD_FINISH, false);
            if (needHideMusicSelectBar()) {
                mMusicControlView.setVisibility(View.GONE);
            } else {
                mMusicControlView.setVisibility(View.VISIBLE);
            }
        } else {
            changeRecordDisplay(RECORD_FINISH, true);
        }
        mFlStartRecord.setEnabled(true);
    }


    private void selectMusic() {
        intentActivityResultLauncher.launch(new Intent(this,SelectMusicActivity.class), ActivityOptionsCompat.makeCustomAnimation(this, R.anim.activity_fade_in, R.anim.activity_fade_out));

    }

    private boolean deleteMusic() {
        mSelectMusicInfo = null;
        return true;
    }

    private void initCapture() {
        mPresenter.connectLiveWindow(mLiveWindow);
        mIvChangeCamera.setEnabled(mPresenter.getCaptureDeviceCount() > 1);

        try {
            startCapturePreview(false);
        } catch (Exception e) {
            e.printStackTrace();
            Log.e(TAG, "startCapturePreviewException: initCapture failed,under 6.0 device may has no access to camera");
            setCaptureViewEnable(false);
        }
        setCaptureViewEnable(true);
        if (getIntent() != null) {
            Bundle bundle = getIntent().getExtras();
            if (bundle != null) {
                initArScene = bundle.getBoolean("initArScene");
            }
        }
    }

    private boolean startCapturePreview(boolean deviceChanged) {
        return startCapturePreview(deviceChanged, mRecordRatio);
    }

    private boolean startCapturePreview(boolean deviceChanged, float ratio) {
        /*
         * 判断当前引擎状态是否为采集预览状态
         * Determine if the current engine status is the collection preview status
         * */
        if (Math.abs(mCurrentRatio - ratio) < 0.02) {
            return true;
        }
        return tryStartCapturePreview(deviceChanged, ratio);
    }

    private boolean tryStartCapturePreview(boolean deviceChanged, float ratio) {
        /*
         * 判断当前引擎状态是否为采集预览状态
         * Determine if the current engine status is the collection preview status
         * */
        if (deviceChanged || getCurrentEngineState() != NvsStreamingContext.STREAMING_ENGINE_STATE_CAPTUREPREVIEW) {
            boolean result = mPresenter.startCapturePreview(mCurrentDeviceIndex, mPresenter.getCaptureGrade(), ratio);
            if (result) {
                mCurrentRatio = ratio;
            }
            return result;
        }
        return true;
    }


    /**
     * 获取当前引擎状态
     * Get the current engine status
     */
    private int getCurrentEngineState() {
        return mPresenter.getCurrentEngineState();
    }


    private final int RECORD_DEFAULT = 0;
    private final int RECORDING = 1;
    private final int RECORD_FINISH = 2;
    /**
     * 当前录制状态
     *The status of current recording
     */
    private int mCurrRecordStatus = RECORD_DEFAULT;

    private void changeRecordDisplay(int recordState, boolean isPicture) {
        if (RECORD_DEFAULT == recordState) {
            //默认显示 Default display
            changeCaptureDisplay(true);
            if (isPicture) {
                mIvTakePhotoBg.setImageResource(R.mipmap.capture_take_photo);
            } else {
                //视频类型拍摄按钮背景 Video Type Capture Button Background.
                mIvTakePhotoBg.setImageResource(R.mipmap.capture_take_video);
            }

            if (needHideMusicSelectBar()) {
                mMusicControlView.setVisibility(View.GONE);
            } else {
                mMusicControlView.setVisibility(View.VISIBLE);
            }
            mMusicControlView.setVisibility(View.VISIBLE);
            mStartText.setVisibility(View.INVISIBLE);
            tvZoom.setVisibility(View.VISIBLE);
            mBeautyLayout.setVisibility(View.VISIBLE);
            mFilterLayout.setVisibility(View.VISIBLE);
            mFuLayout.setVisibility(View.VISIBLE);
            mDelete.setVisibility(View.INVISIBLE);
            mNext.setVisibility(View.INVISIBLE);

            mTvChoosePicture.setVisibility(View.VISIBLE);
            mTvChooseVideo.setVisibility(View.VISIBLE);
        } else if (RECORDING == recordState) {
            //拍摄中 Recording
            changeCaptureDisplay(false);
            tvZoom.setVisibility(View.INVISIBLE);
            if (isPicture) {
                mRecordTime.setVisibility(View.INVISIBLE);
            } else {
                mIvTakePhotoBg.setImageResource(R.mipmap.capture_stop_video);
                if (mFlMiddleParent.getVisibility() != View.VISIBLE) {
                    mFlMiddleParent.setVisibility(View.VISIBLE);
                }
                mRecordTime.setVisibility(View.VISIBLE);
            }
            mMusicControlView.setVisibility(View.GONE);
            mStartText.setVisibility(View.INVISIBLE);

            mBeautyLayout.setVisibility(View.INVISIBLE);
            mFilterLayout.setVisibility(View.INVISIBLE);
            mFuLayout.setVisibility(View.INVISIBLE);
            mDelete.setVisibility(View.INVISIBLE);
            mNext.setVisibility(View.INVISIBLE);

            mTvChoosePicture.setVisibility(View.INVISIBLE);
            mTvChooseVideo.setVisibility(View.INVISIBLE);
        } else if (RECORD_FINISH == recordState) {
            //拍摄完毕 Record finish
            changeCaptureDisplay(true);
            if (needHideMusicSelectBar()) {
                mMusicControlView.setVisibility(View.GONE);
            } else {
                mMusicControlView.setVisibility(View.VISIBLE);
            }
            tvZoom.setVisibility(View.INVISIBLE);
            mIvTakePhotoBg.setImageResource(R.mipmap.capture_take_photo_finish);
            mStartText.setVisibility(View.VISIBLE);

            mBeautyLayout.setVisibility(View.VISIBLE);
            mFilterLayout.setVisibility(View.VISIBLE);
            mFuLayout.setVisibility(View.VISIBLE);
            if (mFlMiddleParent.getVisibility() != View.VISIBLE) {
                mFlMiddleParent.setVisibility(View.VISIBLE);
            }
            mDelete.setVisibility(View.VISIBLE);
            mRecordTime.setVisibility(View.VISIBLE);
            mNext.setVisibility(View.VISIBLE);

            mTvChoosePicture.setVisibility(View.VISIBLE);
            mTvChooseVideo.setVisibility(View.VISIBLE);
        }
        if (mRecordTimeList.isEmpty()) {
            mRecordTime.setVisibility(View.INVISIBLE);
        }
    }


    @Override
    public void onCaptureDeviceCapsReady(int captureDeviceIndex) {
        if (captureDeviceIndex != mCurrentDeviceIndex) {
            return;
        }
        mAutoFocusExposureView.setTouchAble(true);
        mPresenter.updateSettingsWithCapability(captureDeviceIndex);
    }

    @Override
    public void onCaptureDevicePreviewResolutionReady(int i) {
    }

    private boolean isLandscape(float rotation) {
        return rotation % DEGREE_PI != 0;
    }

    @Override
    public void onCaptureDevicePreviewStarted(int i) {
        mIsSwitchingCamera = false;
    }

    @Override
    public void onCaptureDeviceError(int i, int i1) {
        setCaptureViewEnable(false);
    }

    @Override
    public void onCaptureDeviceStopped(int i) {

    }

    @Override
    public void onCaptureDeviceAutoFocusComplete(int i, boolean b) {

    }

    @Override
    public void onCaptureRecordingFinished(int i) {
    }

    @Override
    public void onCaptureRecordingError(int i) {

    }

    @Override
    public void onCaptureRecordingDuration(int i, long l) {
        /*
         * 拍视频
         * Take a video
         * */
        if (mRecordType == Constants.RECORD_TYPE_VIDEO && mCurrRecordStatus == RECORDING) {
            if (l >= Constants.MIN_RECORD_DURATION) {
                mFlStartRecord.setEnabled(true);
            }
            mEachRecodingVideoTime = l;
            if (mFlMiddleParent.getVisibility() != View.VISIBLE) {
                mFlMiddleParent.setVisibility(View.VISIBLE);
            }
            mRecordTime.setVisibility(View.VISIBLE);
            String time = TimeFormatUtil.formatUsToString2(mAllRecordingTime + mEachRecodingVideoTime);
            mRecordTime.setText(time);
        }
    }

    @Override
    public void onCaptureRecordingStarted(int i) {

    }


    @Override
    protected void onDestroy() {
        destroy();
        super.onDestroy();
        mTimeDownView.destroy();

    }

    @Override
    protected int bindLayout() {
        return R.layout.capture_activity_capture;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        intentActivityResultLauncher = registerForActivityResult(new ActivityResultContracts.StartActivityForResult(), new ActivityResultCallBack());
    }

    @Override
    protected void initView() {
        /*
         * 页面主要布局
         * Main page layout
         */
        mRecordTypeLayout = findViewById(R.id.ll_chang_pv);
        mFlMiddleParent = findViewById(R.id.fl_middle_parent);

        mRecordTime = findViewById(R.id.tv_timing_num);
        mAutoFocusExposureView = findViewById(R.id.exposure_view);
        mDelete = findViewById(R.id.iv_back_delete);
        mNext = findViewById(R.id.iv_confirm);
        mFlStartRecord = findViewById(R.id.fl_take_photo);
        mStartText = findViewById(R.id.tv_video_num);
        mLiveWindow = findViewById(R.id.lw_window);
        mLiveWindow.setFillMode(NvsLiveWindowExt.FILLMODE_PRESERVEASPECTFIT);
        mIvExit = findViewById(R.id.iv_exit);
        mIvMore = findViewById(R.id.iv_more);
        mIvRatio = findViewById(R.id.iv_ratio);
        mIvChangeCamera = findViewById(R.id.iv_rollover);

        mBeautyLayout = findViewById(R.id.ll_beauty);
        mFilterLayout = findViewById(R.id.ll_filter);
        mMusicControlView = findViewById(R.id.select_music_control_View);
        mFuLayout = findViewById(R.id.ll_props);

        mIvTakePhotoBg = findViewById(R.id.iv_take_photo);
        mTvChoosePicture = findViewById(R.id.tv_take_photos);
        mTvChooseVideo = findViewById(R.id.tv_take_video);
        mTimeDownView = findViewById(R.id.time_down_view);
        mTimerLayout = findViewById(R.id.timer_layout);
        mMultiBottomView = findViewById(R.id.multiply_bottom_view);
        mMultiBottomHelper = new MultiBottomHelper(mMultiBottomView);
        mMultiBottomView.setFragmentManager(getSupportFragmentManager());
        tvZoom = findViewById(R.id.tv_zoom);
        mLinearFilter = findViewById(R.id.linear_filter_sb);
        mSbFilter = findViewById(R.id.filter_sb);
        mSbFilter.setMax(100);
        mSbFilter.setBreakProgress(0);
        mSbFilter.setPointEnable(false);
        mSbFilter.setProgress(100);
        initAutoFocusExposureViewLayout(mRecordRatio, (int) mAutoFocusExposureView.getRotation());
        initCapture();
        setBeautySwitchChecked(true);
        initListener();
    }

    @SuppressWarnings("SuspiciousNameCombination")
    private float initAutoFocusExposureViewLayout(final float reCordRatio, int rotation) {
        mAutoFocusExposureView.setTouchAble(false);
        int screenHeight = ScreenUtils.getScreenHeight();
        int screenWidth = ScreenUtils.getScreenWidth();
        float screenRatio = screenWidth * 1.0F / screenHeight;
        rotation = rotation % DEGREE_PI;
        int width, height;
        if (reCordRatio > screenRatio) {
            width = screenWidth;
            height = (int) (screenWidth / reCordRatio);
        } else {
            height = screenHeight;
            width = (int) (screenHeight * reCordRatio);
        }
        ViewGroup.LayoutParams layoutParams = mAutoFocusExposureView.getLayoutParams();
        if (rotation != 0) {
            int temp = width;
            width = height;
            height = temp;
            layoutParams.width = screenHeight;
            layoutParams.height = screenWidth;
        } else {
            layoutParams.width = screenWidth;
            layoutParams.height = screenHeight;
        }
        mAutoFocusExposureView.setLayoutParams(layoutParams);
        mAutoFocusExposureView.changeInnerSize(width, height, layoutParams.width, layoutParams.height);
        return width * 1F / height;
    }


    private void changZoomViewLocation(RectF rectF, float rotation, int transY) {
        rotation = rotation % 180;
        float bottom = rectF.bottom;
        if (rotation != 0) {
            bottom = rectF.right;
        }
        int[] location = new int[2];
        mFlStartRecord.getLocationOnScreen(location);
        int statusBarHeight = ScreenUtils.getStatusBarHeight();
        int padding = 30;
        int viewHeight = tvZoom.getHeight();
        int top = (location[1] - statusBarHeight - padding - viewHeight);
        int y = (int) (bottom - statusBarHeight - padding - viewHeight) + transY;
        if (y > top) {
            y = top;
        }
        tvZoom.setY(y);
    }


    @Override
    protected void onResume() {
        super.onResume();
        mNext.setClickable(true);
        tryStartCapturePreview(false, mCurrentRatio);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (getCurrentEngineState() == NvsStreamingContext.STREAMING_ENGINE_STATE_CAPTURERECORDING) {
            stopRecording();
        }
    }

    private void destroy() {
        setStreamingCallback(true);
        mPresenter.release();
        mRecordTimeList.clear();
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                //删除录制的文件,没有保存过的 Delete recorded files that have not been saved.
                for (String filePath : mRecordFileList) {
                    FileUtils.delete(filePath);
                }
                mRecordFileList.clear();
            }
        });
        MakeupManager.getInstacne().clearAllData();
        AudioPlayer.getInstance().destroyPlayer();
    }

    private void setStreamingCallback(boolean isDestroyCallback) {
        mPresenter.setCaptureDeviceCallback(isDestroyCallback ? null : this);
        if(isDestroyCallback){
            mPresenter.unregisterCallbackObserver(mCallbackObserver);
        }else{
            mPresenter.registerCallbackObserver(mCallbackObserver);
        }
        mPresenter.setCaptureRecordingDurationCallback(isDestroyCallback ? null : this);
        mPresenter.setCaptureRecordingStartedCallback(isDestroyCallback ? null : this);
        mPresenter.setCaptureRecordingFrameReachedCallback(isDestroyCallback ? null : this);
    }

    private void takePhoto(long time) {
        if (mCurRecordVideoPath != null) {
            NvsVideoFrameRetriever videoFrameRetriever = mPresenter.createVideoFrameRetriever(mCurRecordVideoPath);
            if (videoFrameRetriever != null) {
                int screenHeight = ScreenUtils.getScreenHeight();
                screenHeight = (int) (screenHeight / 16) * 16;
                //videoFrameRetriever.getFrameAtTimeWithCustomVideoFrameHeight 需要传入被16整除的数字
                // Need to pass in a number divisible by 16.
                Bitmap pictureBitmap = videoFrameRetriever.getFrameAtTimeWithCustomVideoFrameHeight(time, screenHeight);
                LogUtils.d("takePhoto", " 被16整除的height" + screenHeight + "  screen: " + ScreenUtils.getScreenWidth() + " " + ScreenUtils.getScreenHeight() + "**bitmap=" + pictureBitmap);
                if (pictureBitmap != null) {
                    /*
                     * 拍照片
                     * Take a photo
                     */
                    if (mRecordType == Constants.RECORD_TYPE_PICTURE) {
                        mAllRecordingTime += mEachRecodingImageTime;
                        mRecordTimeList.add(mEachRecodingImageTime);
                        mRecordTime.setText(TimeFormatUtil.formatUsToString2(mAllRecordingTime));
                        mStartText.setText(String.format("%d", mRecordTimeList.size()));
                        String jpgPath = PathUtils.getRecordPicturePath();
                        if (ImageUtils.save(pictureBitmap, jpgPath, Bitmap.CompressFormat.JPEG)) {
                            mRecordFileList.add(jpgPath);
                        }
                        if (mCurRecordVideoPath != null) {
                            File file = new File(mCurRecordVideoPath);
                            if (file.exists()) {
                                file.delete();
                            }
                        }
                        mRecordTime.setVisibility(View.VISIBLE);
                        if (needHideMusicSelectBar()) {
                            mMusicControlView.setVisibility(View.GONE);
                        }
                        mFlStartRecord.setEnabled(true);
                    }

                } else {
                    changeRecordDisplay(RECORD_DEFAULT, true);
                }
                videoFrameRetriever.release();
            }
        }
    }

    private void selectRecordType(boolean ivPicture) {
        int[] location = new int[2];
        //获取在当前窗口内的绝对坐标
        // Obtain absolute coordinates within the current window
        mFlStartRecord.getLocationInWindow(location);
        float middleX = location[0] + mFlStartRecord.getWidth() / 2f;
        float targetX;
        if (ivPicture) {
            if (mRecordType == Constants.RECORD_TYPE_PICTURE) {
                return;
            }
            mTvChoosePicture.getLocationInWindow(location);
            targetX = location[0] + mTvChooseVideo.getWidth() / 2f;
            mTvChoosePicture.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            mTvChooseVideo.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            if (!hasRecord()) {
                mIvTakePhotoBg.setImageResource(R.mipmap.capture_take_photo);
            }
            mRecordType = Constants.RECORD_TYPE_PICTURE;
        } else {
            if (mRecordType == Constants.RECORD_TYPE_VIDEO) {
                return;
            }
            targetX = middleX;
            mTvChooseVideo.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
            mTvChoosePicture.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
            if (!hasRecord()) {
                mIvTakePhotoBg.setImageResource(R.mipmap.capture_take_video);
            }
            mRecordType = Constants.RECORD_TYPE_VIDEO;
        }
        // Log.d("lhz","middleX="+middleX+"**targetX="+targetX+"**ex="+(middleX-targetX));
        ObjectAnimator animator = ObjectAnimator.ofFloat(mRecordTypeLayout, "translationX", middleX - targetX);
        animator.setDuration(300);
        animator.start();
    }


    public boolean hasRecord() {
        return mRecordFileList.size() != 0;
    }

    @Override
    public void onBackPressed() {
        if (mMultiBottomHelper.isShow()) {
            mMultiBottomHelper.hide();
            return;
        }
        super.onBackPressed();
    }

    public void setCaptureViewEnable(boolean enable) {

    }

    @Override
    public void onRecordingFirstVideoFrameReached(int i, final long l) {
        if (mRecordType == Constants.RECORD_TYPE_PICTURE) {
            stopRecording();
            final Handler mainHandler = new Handler(Looper.getMainLooper());
            mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    takePhoto(l);
                }
            });
        }
    }

    final EngineCallbackObserver mCallbackObserver = new EngineCallbackObserver() {
        @Override
        public boolean isActive() {
            return !isDestroyed();
        }

        @Override
        public void onFirstVideoFramePresented(NvsTimeline nvsTimeline) {
            int liveWindowTransY = 0;
            float rotation = mViewRotation;
            if (isLandscape(rotation)) {
                rotation = 1F / rotation;
            }
            if (mRealRecordRatio > CommonData.AspectRatio.ASPECT_9V16.getRatio()) {
                liveWindowTransY = -SizeUtils.dp2px(35);
            }
            mLiveWindow.setTranslationY(liveWindowTransY);
            mAutoFocusExposureView.setTranslationY(liveWindowTransY);
            changZoomViewLocation(mAutoFocusExposureView.getTouchRectF(), rotation, liveWindowTransY);
        }
    };

            class ActivityResultCallBack implements ActivityResultCallback<ActivityResult> {
                @Override
                public void onActivityResult(ActivityResult result) {
                    Intent data = result.getData();
                    int resultCode = result.getResultCode();
                    if (resultCode == RESULT_OK && data != null) {
                        Serializable extra = data.getSerializableExtra(BUNDLE_DATA);
                        //音乐时间从下一次开始拍摄或录制的图片或视频开始，前面的图片或视频音乐去掉
                        //The music time starts from the next picture or video taken or recorded,
                        // and the previous picture or video music is removed.
                        mMusicStartIndex = mRecordFileList.size();
                        if (extra instanceof MusicInfo) {
                            mSelectMusicInfo = (MusicInfo) extra;
                            AudioPlayer.getInstance().setCurrentMusic(mSelectMusicInfo, true);
                            mMusicControlView.setText(mSelectMusicInfo.getTitle());
                        }
                    }
                }
            }

            private long getPlayPosition(){
                if (mSelectMusicInfo != null) {
                    long allRecordingTime = mAllRecordingTime;
                    if (mMusicStartIndex == mRecordFileList.size()) {
                        allRecordingTime = 0;
                    }
                    long trimIn = mSelectMusicInfo.getTrimIn();
                    return trimIn + allRecordingTime % (mSelectMusicInfo.getTrimOut() - trimIn) ;
                }
                return 0;
            }

}
