<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingBottom="@dimen/dp_px_50"
    android:background="@color/black">

    <ImageView
        android:id="@+id/iv_rename"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_49"
        android:contentDescription="@null"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:src="@mipmap/draft_manager_rename" />

    <TextView
        android:id="@+id/tv_rename"
        android:layout_width="0dp"
        android:layout_height="21dp"
        android:layout_marginStart="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manage_rename"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintBottom_toBottomOf="@+id/iv_rename"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_rename"
        app:layout_constraintStart_toEndOf="@+id/iv_rename"
        app:layout_constraintTop_toTopOf="@+id/iv_rename" />


    <ImageView
        android:id="@+id/iv_copy"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_76"
        app:layout_constraintTop_toBottomOf="@+id/iv_rename"
        app:layout_constraintLeft_toLeftOf="parent"
        android:contentDescription="@null"
        android:src="@mipmap/draft_manager_copy" />

    <TextView
        android:id="@+id/tv_copy"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manager_copy"
        android:textColor="@color/white_8"
        app:layout_constraintTop_toTopOf="@+id/iv_copy"
        app:layout_constraintBottom_toBottomOf="@+id/iv_copy"
        app:layout_constraintLeft_toRightOf="@+id/iv_copy"
        app:layout_constraintStart_toEndOf="@+id/iv_copy"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="@dimen/sp_px_36" />

    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_78"
        android:contentDescription="@null"
        android:src="@mipmap/draft_manager_delete"
        app:layout_constraintTop_toBottomOf="@+id/iv_copy"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_delete"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manager_delete"
        android:textColor="@color/white_8"
        app:layout_constraintTop_toTopOf="@+id/iv_delete"
        app:layout_constraintBottom_toBottomOf="@+id/iv_delete"
        app:layout_constraintLeft_toRightOf="@+id/iv_delete"
        app:layout_constraintStart_toEndOf="@+id/iv_delete"
        app:layout_constraintEnd_toEndOf="parent"
        android:textSize="@dimen/sp_px_36" />

    <ImageView
        android:id="@+id/iv_cloud_compile"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_78"
        android:contentDescription="@null"
        app:layout_constraintTop_toBottomOf="@+id/iv_delete"
        app:layout_constraintLeft_toLeftOf="parent"
        android:src="@drawable/ic_cloud_compiler" />

    <TextView
        android:id="@+id/tv_cloud_compile"
        android:layout_width="0dp"
        android:layout_height="21dp"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manage_cloud_compile"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36"
        app:layout_constraintBottom_toBottomOf="@+id/iv_cloud_compile"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/iv_cloud_compile"
        app:layout_constraintStart_toEndOf="@+id/iv_cloud_compile"
        app:layout_constraintTop_toTopOf="@+id/iv_cloud_compile" />

    <ImageView
        android:id="@+id/iv_upload"
        android:layout_width="@dimen/dp_px_45"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_78"
        android:contentDescription="@null"
        android:src="@mipmap/ic_draft_upload"
        app:layout_constraintTop_toBottomOf="@+id/tv_cloud_compile"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

    <TextView
        android:id="@+id/tv_upload"
        android:layout_width="0dp"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginStart="@dimen/dp_px_45"
        android:gravity="center_vertical"
        android:text="@string/draft_manager_upload"
        android:textColor="@color/white_8"
        app:layout_constraintTop_toTopOf="@+id/iv_upload"
        app:layout_constraintBottom_toBottomOf="@+id/iv_upload"
        app:layout_constraintLeft_toRightOf="@+id/iv_upload"
        app:layout_constraintStart_toEndOf="@+id/iv_upload"
        android:textSize="@dimen/sp_px_36" />

    <TextView
        android:id="@+id/tv_upload_hint"
        android:layout_width="0dp"
        android:gravity="center|end"
        android:layout_height="wrap_content"
        android:textSize="@dimen/sp_px_30"
        android:layout_marginEnd="@dimen/dp_px_45"
        android:text="@string/draft_manager_upload_hint"
        android:textColor="@color/color_ffff365E"
        app:layout_constraintBottom_toBottomOf="@+id/tv_upload"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_upload"
        app:layout_constraintTop_toTopOf="@+id/tv_upload" />

</androidx.constraintlayout.widget.ConstraintLayout>
