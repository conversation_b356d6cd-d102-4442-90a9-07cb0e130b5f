# Video Edit SDK

A comprehensive video editing SDK that integrates all the functionality from the MYVideo Android application.

## Features

- Video editing and processing
- Media selection and management
- Template-based video creation
- And more...

## Integration

### Gradle

Add the following to your project's build.gradle file:

```groovy
repositories {
    maven {
        url 'path/to/your/repo'
    }
}

dependencies {
    implementation 'com.deep.foresight:videoeditsdk:1.0.0'
}
```

## Usage

### Initialization

Initialize the SDK in your Application class:

```java
public class MyApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();
        VideoEditSDK.init(this);
    }
}
```

### Launch Material Selection

```java
// Get the SDK instance
VideoEditSDK sdk = VideoEditSDK.getInstance();

// Launch material selection
List<TemplateClip> clipList = new ArrayList<>();
sdk.launchMaterialSelection(
    Constants.TEMPLATE_TYPE_NORMAL,
    Constants.MEDIA_TYPE_ALL,
    clipList,
    "templateId",
    true
);
```

## Building the SDK

To build the SDK, run the following command:

```bash
./gradlew :videoeditsdk:assembleRelease
```

This will generate the AAR file in `videoeditsdk/build/outputs/aar/`.

To publish the SDK to your local Maven repository:

```bash
./gradlew :videoeditsdk:publishReleasePublicationToLocalRepoRepository
```

This will publish the SDK to `videoeditsdk/build/repo/`.

## License

[Include your license information here]
