package com.meishe.draft.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/5/18 15:53
 * @Description :任务信息 entity. The entity of job information.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class JobInfoEntity {
   @PrimaryKey
   @NonNull
   private String id = "123456789";

   private String jobId;
   private String projectUuid;
   private String coverUrl;
   private String duration;
   private String createAt;
   private String title;
   private String fileSize;

   @NonNull
   public String getId() {
      return id;
   }

   public void setId(@NonNull String id) {
      this.id = id;
   }

   public String getJobId() {
      return jobId;
   }

   public void setJobId(String jobId) {
      this.jobId = jobId;
   }

   public String getCoverUrl() {
      return coverUrl;
   }

   public void setCoverUrl(String coverUrl) {
      this.coverUrl = coverUrl;
   }

   public String getDuration() {
      return duration;
   }

   public void setDuration(String duration) {
      this.duration = duration;
   }

   public String getCreateAt() {
      return createAt;
   }

   public void setCreateAt(String createAt) {
      this.createAt = createAt;
   }

   public String getTitle() {
      return title;
   }

   public void setTitle(String title) {
      this.title = title;
   }

   public String getFileSize() {
      return fileSize;
   }

   public void setFileSize(String fileSize) {
      this.fileSize = fileSize;
   }

   public String getProjectUuid() {
      return projectUuid;
   }

   public void setProjectUuid(String projectUuid) {
      this.projectUuid = projectUuid;
   }
}
