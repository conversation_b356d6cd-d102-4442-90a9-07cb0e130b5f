package com.meishe.myvideoapp.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.czc.cutsame.TemplateListActivity;
import com.meishe.base.BuildConfig;
import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseActivity;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.base.utils.LogUtils;
import com.meishe.base.utils.PermissionConstants;
import com.meishe.base.utils.PermissionUtils;
import com.meishe.base.utils.ToastUtils;
import com.meishe.base.utils.Utils;

import com.meishe.engine.EditorEngine;
import com.meishe.engine.asset.AssetsManager;
import com.meishe.engine.util.PathUtils;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.logic.manager.PreferencesManager;
import com.meishe.myvideo.activity.MaterialSelectActivity;
import com.meishe.myvideo.activity.TestActivity;
import com.meishe.myvideo.fragment.EditingFragment;
import com.meishe.myvideoapp.R;
import com.meishe.myvideoapp.fragment.NewMainFragment;
import com.meishe.myvideoapp.util.ConfigUtil;
import com.meishe.myvideoapp.util.CrashHandler;
import com.meishe.myvideoapp.view.pop.PrivacyPolicyPop;

import java.lang.reflect.Method;
import java.util.List;

import static com.meishe.base.utils.PermissionConstants.CAMERA;
import static com.meishe.base.utils.PermissionConstants.MICROPHONE;
import static com.meishe.base.utils.PermissionConstants.NET;
import static com.meishe.base.utils.PermissionConstants.SENSORS;
import static com.meishe.base.utils.PermissionConstants.STORAGE;
import static com.meishe.base.utils.PermissionConstants.LOCATION;
import static com.meishe.logic.constant.PagerConstants.BUNDLE_SAVE_DRAFT;
import static com.meishe.logic.constant.PagerConstants.FROM_MAIN_PAGE;
import static com.meishe.logic.constant.PagerConstants.FROM_PAGE;
import static com.meishe.myvideo.event.MessageEvent.MESSAGE_REQUEST_PERMISSION;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有www.meishesdk.com设计
 *
 * <AUTHOR> LiCong
 * @CreateDate : 2025/05/20
 * @Description : 新的首页 New Home page
 * @Copyright : www.meishesdk.com Inc.All rights reserved.
 */
public class NewMainActivity extends BaseActivity {
    private NewMainFragment mMainFragment;

    @Override
    protected int bindLayout() {
        return R.layout.activity_new_main;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        Utils.hideStatusBar(this);
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void initView() {
        mMainFragment = NewMainFragment.newInstance();
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, mMainFragment)
                .commit();
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            Bundle bundle = intent.getExtras();
            if (bundle != null) {
                boolean saveDraft = bundle.getBoolean(BUNDLE_SAVE_DRAFT, false);
                if (saveDraft) {
                    CrashHandler.getInstance().saveDraft();
                }
            }
        }
        
        if (mMainFragment != null) {
            mMainFragment.onNewIntent(intent);
        }
    }

    @Override
    protected void requestData() {
    }

    @Override
    protected void onResume() {
        super.onResume();
        LogUtils.d("NewMainActivity", "onResume");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mMainFragment != null) {
            mMainFragment.onActivityResult(requestCode, resultCode, data);
        }
    }
}
