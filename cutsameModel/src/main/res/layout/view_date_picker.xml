<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="297dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <TextView
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:padding="@dimen/dp_px_30"
        android:layout_marginStart="@dimen/dp_px_9"
        android:text="@string/cancel"
        android:textColor="@color/color_ff363636"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginTop="@dimen/dp_px_45"
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:text="@string/view_data_picker_title"
        android:textColor="@color/color_ff363636"
        android:textSize="@dimen/sp_px_42" />

    <TextView
        app:layout_constraintTop_toTopOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="@+id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:padding="@dimen/dp_px_30"
        android:layout_marginEnd="@dimen/dp_px_9"
        android:text="@string/confirm"
        android:textColor="@color/color_ff363636"
        android:textSize="@dimen/sp_px_42" />

    <View
        app:layout_constraintTop_toTopOf="@+id/pick_layout"
        app:layout_constraintBottom_toBottomOf="@+id/pick_layout"
        android:layout_width="match_parent"
        
android:layout_marginBottom="@dimen/dp_px_120"
        android:layout_height="0.5dp"
        android:background="@color/black_10">
    </View>

    <View
        app:layout_constraintTop_toTopOf="@+id/pick_layout"
        app:layout_constraintBottom_toBottomOf="@+id/pick_layout"
        android:layout_marginTop="@dimen/dp_px_120"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/black_10">
    </View>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginEnd="@dimen/dp_px_103"
        android:layout_marginStart="@dimen/dp_px_45"
        android:id="@+id/pick_layout"
        android:layout_marginTop="@dimen/dp_px_120"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="230dp">
        <com.czc.cutsame.view.DatePickerView
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/pv_minute"
            android:id="@+id/pv_hour"
            android:layout_width="0dp"
            android:layout_height="match_parent" />
        <com.czc.cutsame.view.DatePickerView
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintLeft_toRightOf="@+id/pv_hour"
            app:layout_constraintRight_toLeftOf="@+id/pv_second"
            android:id="@+id/pv_minute"
            android:layout_width="0dp"
            android:layout_weight="3"
            android:layout_height="match_parent" />
        <com.czc.cutsame.view.DatePickerView
            app:layout_constraintLeft_toRightOf="@+id/pv_minute"
            app:layout_constraintRight_toLeftOf="@+id/pv_frame"
            app:layout_constraintHorizontal_weight="2"
            android:id="@+id/pv_second"
            android:layout_width="0dp"
            android:layout_weight="3"
            android:layout_height="match_parent" />
        <com.czc.cutsame.view.DatePickerView
            app:layout_constraintHorizontal_weight="2"
            app:layout_constraintLeft_toRightOf="@+id/pv_second"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/pv_frame"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>