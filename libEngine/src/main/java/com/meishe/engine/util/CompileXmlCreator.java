package com.meishe.engine.util;

import android.text.TextUtils;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.bean.template.ExportTemplateDescInfo;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/6/10 15:36
 * @Description :合成xml创建类 The compile xml creator
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CompileXmlCreator {
    public static final String TAG_HOME = "interactpro";
    public static final String TAG_ASSETS = "assets";
    public static final String TAG_FOOTAGES = "footages";
    public static final String TAG_ASPECT_RATIO = "aspectRatio";
    public static final String TAG_SIZE_LEVEL= "sizeLevel";
    public static final String TAG_PROJECT_URL= "projectUrl";
    public static final String TAG_PROJECT_UUID= "projectUuid";
    public static final String TAG_FPS = "fps";

    private static Document createTemplateDocument(){
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document document = null;
        String test = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<interactpro aspectRatio=\"9:16\" sizeLevel=\"720\" fps =\"30\" projectUrl=\"\" projectUuid=\"\">\n" +
                " <assets>\n" +
                " </assets>\n" +
                " <footages>\n" +
                " </footages>\n" +
                "</interactpro>\n";
        try {
            DocumentBuilder db = dbf.newDocumentBuilder();
            InputStream is = new ByteArrayInputStream(test.getBytes(StandardCharsets.UTF_8));
            document = db.parse(is);
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return document;
    }

    public static String fillData(String aspectRatio, String sizeLevel, String projectUrl, String projectUuid, String fps, List<ExportTemplateDescInfo.FootageInfo> footageInfoList, List<ExportTemplateDescInfo.InnerAsset> assetList){
        Document document = createTemplateDocument();
        if (document == null) {
            return null;
        }
        NodeList homeTag = document.getElementsByTagName(TAG_HOME);
        NamedNodeMap attributes =  homeTag.item(0).getAttributes();
        attributes.getNamedItem(TAG_ASPECT_RATIO).setNodeValue(aspectRatio);
        attributes.getNamedItem(TAG_SIZE_LEVEL).setNodeValue(sizeLevel);
        attributes.getNamedItem(TAG_PROJECT_URL).setNodeValue(projectUrl);
        attributes.getNamedItem(TAG_PROJECT_UUID).setNodeValue(projectUuid);
        attributes.getNamedItem(TAG_FPS).setNodeValue(fps);

        if (!CommonUtils.isEmpty(footageInfoList)) {
            Node item = document.getElementsByTagName(TAG_FOOTAGES).item(0);
            for (ExportTemplateDescInfo.FootageInfo footageInfo : footageInfoList) {
                addParam(document, (Element) item, footageInfo);
            }
        }
        if (!CommonUtils.isEmpty(assetList)) {
            Node item = document.getElementsByTagName(TAG_ASSETS).item(0);
            for (ExportTemplateDescInfo.InnerAsset innerAsset : assetList) {
                addParam(document, (Element) item, innerAsset);
            }
        }
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer;
        try {
            transformer = transformerFactory.newTransformer();
            transformer.setOutputProperty("encoding", "UTF-8");
            StringWriter stringWriter = new StringWriter();
            transformer.transform(new DOMSource(document), new StreamResult(stringWriter));
            return stringWriter.toString();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void addParam(Document document, Element parent, ExportTemplateDescInfo.FootageInfo footageInfo) {
        Element key = document.createElement("footage");
        String tem = footageInfo.url.toLowerCase();
        if (tem.endsWith(".jpg") || tem.endsWith(".png")) {
            footageInfo.type = "internal";
        }
        key.setAttribute("path", footageInfo.url);
        key.setAttribute("type", footageInfo.type);
        key.setAttribute("footageId", footageInfo.id);
        if (!TextUtils.isEmpty(footageInfo.extraData)) {
            key.setAttribute("extraData", footageInfo.extraData);
        }
        if (!TextUtils.isEmpty(footageInfo.alphaUrl)) {
            key.setAttribute("alphaUrl", footageInfo.alphaUrl);
        }
        parent.appendChild(key);
    }

    private static void addParam(Document document, Element parent, ExportTemplateDescInfo.InnerAsset asset) {
        Element key = document.createElement("asset");
        key.setAttribute("id", asset.uuid);
        parent.appendChild(key);
    }
}
