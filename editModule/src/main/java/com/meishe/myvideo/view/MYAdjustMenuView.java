package com.meishe.myvideo.view;

import android.content.Context;
import android.widget.TextView;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.adapter.AdjustAdapter;
import com.meishe.myvideo.adapter.BaseSelectAdapter;
import com.meishe.myvideo.view.base.BaseConfirmMenuView;
import com.meishe.myvideo.view.presenter.AdjustPresenter;
import com.meishe.myvideo.view.presenter.BaseConfirmPresenter;

import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/25 14:04
 * @Description :调节菜单, Adjust menu
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 * 调整菜单视图
 * Adjusting Menu View
 */
public class MYAdjustMenuView extends BaseConfirmMenuView {


    public MYAdjustMenuView(Context context) {
        super(context);
        mAssetsTypeTab.setVisibility(GONE);
    }

    @Override
    public BaseSelectAdapter<IBaseInfo> getAdapter() {
        if (mAdapter == null) {
            mAdapter = new AdjustAdapter();
        }
        return mAdapter;
    }

    @Override
    protected void onItemClicked(IBaseInfo baseInfo, boolean isSelected) {
        if (baseInfo != null) {
            showSeekBar();
            setSeekPress(baseInfo);
            mPresenter.onItemClicked(baseInfo, isSelected);
        }
    }

    @Override
    protected void setContentText(TextView textView) {
        textView.setText(R.string.main_menu_name_adjust);
    }

    @Override
    protected BaseConfirmPresenter<? extends BaseConfirmMenuView> getPresenter() {
        AdjustPresenter presenter = new AdjustPresenter();
        presenter.attachView(this);
        return presenter;
    }

    /**
     * Update view.
     * 更新视图
     *
     * @param data the data
     */
    public void updateView(List<IBaseInfo> data) {
        mAdapter.setNewData(data);
    }

    @Override
    protected int getItemLayoutResId() {
        return R.layout.view_menu_adjust;
    }

    public boolean needShowApply() {
        return mNeedShowApply;
    }
}
