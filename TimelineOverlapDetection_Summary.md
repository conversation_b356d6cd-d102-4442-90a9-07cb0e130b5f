# 时间轴重叠检测与多行绘制逻辑总结

## 🎯 核心重叠检测算法

### 📍 重叠检测的三个关键条件 (TrackViewLayout.java:775-777)

```java
// 条件1: 新效果开始时间 <= 现有效果开始时间 && 新效果结束时间 > 现有效果开始时间
if ((newInPoint <= inPoint && newOutPoint > inPoint)
    // 条件2: 新效果结束时间 > 现有效果结束时间 && 新效果开始时间 < 现有效果结束时间  
    || (newOutPoint > outPoint && newInPoint < outPoint)
    // 条件3: 新效果开始时间 > 现有效果开始时间 && 新效果结束时间 < 现有效果结束时间
    || (newInPoint > inPoint && newOutPoint < outPoint)) {
    return false; // 存在重叠，不能添加到此轨道
}
```

### 🔄 轨道自动分配策略

#### 音频轨道分配算法 (DraftEditPresenter.java:346-372)
```java
public int getNextAudioClipTrackIndex(long inPoint) {
    int trackIndex = 0;
    int audioTrackCount = mMeicamTimeline.getAudioTrackCount();
    boolean isAppend = false;
    
    // 遍历所有现有轨道
    for (int i = 0; i < audioTrackCount; i++) {
        MeicamAudioTrack audioTrack = mMeicamTimeline.getAudioTrack(i);
        MeicamAudioClip audioClip = audioTrack.getAudioClip(clipCount - 1);
        
        // 如果新效果入点 >= 最后一个效果的出点，可以添加到此轨道
        if (audioClip == null || inPoint >= audioClip.getOutPoint()) {
            trackIndex = i;
            isAppend = true;
            break;
        }
    }
    
    // 如果所有轨道都不能添加，创建新轨道
    if (!isAppend) {
        trackIndex = Math.min(audioTrackCount, CommonData.MAX_AUDIO_COUNT);
    }
    return trackIndex;
}
```

## 🎨 多行绘制布局逻辑

### 📍 轨道视觉布局计算 (TrackViewLayout.java:1177-1191)

```java
// 1. 计算最大轨道数量
mMaxTrackCount = Math.max(mMaxTrackCount, getMaxTrackIndex(integerListHashMap) + 1);

// 2. 设置容器总高度
layoutParams.height = mMaxTrackCount * mTrackHeight + mTrackViewMarginTop;

// 3. 为每个效果创建视图并定位
for (Map.Entry<Integer, List<BaseUIClip>> entry : entries) {
    for (BaseUIClip baseUIClip : clipList) {
        BaseItemView itemView = new BaseItemView(mContext);
        FrameLayout.LayoutParams layoutParams2 = new FrameLayout.LayoutParams(...);
        
        // 水平位置：基于时间计算
        layoutParams2.leftMargin = getChildTopMarginFromDuration(baseUIClip.getInPoint());
        
        // 垂直位置：基于轨道索引计算  
        layoutParams2.topMargin = mTrackHeight * entry.getKey() + mTrackViewMarginTop;
        
        itemView.setLayoutParams(layoutParams2);
        mTrackViewVerticalParent.addView(itemView);
    }
}
```

## 🔧 关键计算公式

### 时间到像素位置转换
```java
// 水平位置计算
private int getChildTopMarginFromDuration(long duration) {
    return mStartPadding + PixelPerMicrosecondUtil.durationToLength(duration);
}

// 垂直位置计算  
public int getTopHeightByTrackIndex(int trackIndex) {
    return mTrackHeight * trackIndex;
}

// 效果时长计算
long outPoint = (long) (inPoint + (clip.getTrimOut() - clip.getTrimIn()) / clip.getSpeed());
```

## 📊 数据结构组织

### 轨道数据存储
```java
// 使用HashMap存储轨道数据，Key为轨道索引，Value为该轨道上的效果列表
HashMap<Integer, List<BaseUIClip>> mIntegerListHashMap = new HashMap<>();

// 轨道索引从0开始，每个轨道可以包含多个不重叠的效果
// Track 0: [Effect1, Effect3, Effect5]  
// Track 1: [Effect2, Effect4]
// Track 2: [Effect6]
```

## 🎯 重叠检测的实际应用场景

### 场景1: 添加新音频效果
1. 用户在时间轴5秒位置添加音频
2. 系统检查轨道0：发现3-8秒已有音频，存在重叠
3. 系统检查轨道1：发现该时间段空闲
4. 将新音频分配到轨道1

### 场景2: 拖拽效果到新位置
1. 用户拖拽效果从轨道0移动到轨道1的某个时间点
2. 系统实时检测目标位置是否与轨道1现有效果重叠
3. 如果重叠，阻止放置；如果不重叠，允许放置

## 🔄 动态轨道管理

### 轨道数量动态调整
```java
// 最大轨道数限制
private static final int MAX_TRACK_COUNT = 4;

// 根据实际使用情况动态调整轨道数量
mMaxTrackCount = Math.max(mMaxTrackCount, getMaxTrackIndex(integerListHashMap) + 1);

// 轨道容器高度自适应
layoutParams.height = mMaxTrackCount * mTrackHeight + mTrackViewMarginTop;
```

## 🎨 视觉效果实现

### 轨道背景和分隔
- 每个轨道高度固定：`mTrackHeight = 40dp`
- 轨道间距：`mTrackViewMarginTop = 6dp`  
- 不同类型效果使用不同背景色区分

### 效果视图定位
- X轴位置：基于时间戳精确计算像素位置
- Y轴位置：基于轨道索引计算垂直偏移
- 宽度：基于效果时长计算像素宽度

这套逻辑确保了时间轴上的效果能够：
1. **精确避免重叠**：通过严格的时间范围检测
2. **自动分配轨道**：智能选择合适的轨道位置  
3. **视觉清晰呈现**：多行布局清晰展示所有效果
4. **支持交互操作**：拖拽、选择、编辑等用户操作
