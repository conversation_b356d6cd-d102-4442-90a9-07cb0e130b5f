package com.meishe.engine.bean;

import androidx.annotation.NonNull;

import com.meicam.sdk.NvsObject;
import com.meishe.engine.adapter.TimelineDataParserAdapter;
import com.meishe.engine.local.LNvMaskModel;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/7/21 13:18
 * @Description: 蒙版数据 The mask model
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class NvMaskModel implements Serializable, Cloneable, TimelineDataParserAdapter<LNvMaskModel> {
    public int maskType;
    public boolean inverseRegion = false;
    public float rectRate = 2;
    /**
     * 圆角 和短边的比例
     * The radio of filleted corner and short edges
     */
    public float cornerRadiusRate = 0;
    public float circleRate = 0.7f;
    public float horizontalScale = 1;
    public float verticalScale = 1;
    public float feather = 0;
    public float heightRateOfWidth = 9.0f / 10 / 5.0f;
    public int maxNumberWords = 10;

    public String text = "VIDEO";
    public MeicamMaskRegionInfo regionInfo;
    public Transform transform = new Transform();


    public float assetsWidth;
    public float assetsHeight;

    public void reset() {
        inverseRegion = false;
        rectRate = 2;
        cornerRadiusRate = 0;
        circleRate = 0.7f;
        horizontalScale = 1;
        verticalScale = 1;
        feather = 0;
        heightRateOfWidth = 9.0f / 10 / 5.0f;
    }

    @Override
    public LNvMaskModel parseToLocalData() {
        LNvMaskModel lNvMaskModel = new LNvMaskModel();
        lNvMaskModel.maskType = maskType;
        lNvMaskModel.inverseRegion = inverseRegion;
        lNvMaskModel.rectRate = rectRate;
        lNvMaskModel.cornerRadiusRate = cornerRadiusRate;
        lNvMaskModel.circleRate = circleRate;
        lNvMaskModel.horizontalScale = horizontalScale;
        lNvMaskModel.verticalScale = verticalScale;
        lNvMaskModel.feather = feather;
        lNvMaskModel.heightRateOfWidth = heightRateOfWidth;
        lNvMaskModel.text = text;
        lNvMaskModel.transform = transform;
        return lNvMaskModel;
    }

    @Override
    public void recoverFromLocalData(LNvMaskModel lNvMaskModel) {
        maskType = lNvMaskModel.maskType;
        inverseRegion = lNvMaskModel.inverseRegion;
        rectRate = lNvMaskModel.rectRate;
        cornerRadiusRate = lNvMaskModel.cornerRadiusRate;
        circleRate = lNvMaskModel.circleRate;
        horizontalScale = lNvMaskModel.horizontalScale;
        verticalScale = lNvMaskModel.verticalScale;
        feather = lNvMaskModel.feather;
        heightRateOfWidth = lNvMaskModel.heightRateOfWidth;
        text = lNvMaskModel.text;
        transform = lNvMaskModel.transform;
    }

    @Override
    public void recoverFromTimelineData(NvsObject nvsObject) {

    }

    @NonNull
    @Override
    public NvMaskModel clone() {
        try {
            NvMaskModel clone = (NvMaskModel) super.clone();
            clone.regionInfo = regionInfo.clone();
            clone.transform = (Transform) transform.clone();
            return clone;
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return new NvMaskModel();
    }
}
