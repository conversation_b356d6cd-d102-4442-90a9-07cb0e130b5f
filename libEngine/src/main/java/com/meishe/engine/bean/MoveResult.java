package com.meishe.engine.bean;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2022/9/2 10:31
 * @Description: 移动结果 The move result
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class MoveResult implements Serializable {
    public MoveResult(State state, long inPointChange, long outPointChange) {
        this.state = state;
        this.inPointChange = inPointChange;
        this.outPointChange = outPointChange;
    }

    public enum State{
        //back 在后面 移动
        //front No effect 在前面 不影响
        //backCross The rear part coincides with or contains the change interval 后部份和变化区间重合 或 包含区间
        //frontCross The former part coincides with the change interval or the interval contains 前部份和变化区间重合 或 区间包含
        back,
        backCross,
        frontCross,
        front
    }

    public State state;
    public long inPointChange;
    public long outPointChange;

    @Override
    public String toString() {
        return "MoveResult{" +
                "state=" + state +
                ", inPointChange=" + inPointChange +
                ", outPointChange=" + outPointChange +
                '}';
    }
}
