package com.meishe.user.bean;

import com.meishe.draft.db.JobInfoEntity;

import java.io.Serializable;
import java.util.Objects;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/7/20 13:09
 * @Description :视频合成bean The video compile bean.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class VideoCompileBean implements Serializable {
   /**
    * 0:初始状态, 1:完成, 2:失败, 3:超时, 5进行中
    * 0: initial state, 1: completed, 2: failed, 3: timeout, 5 in progress
    */
   public static final int STATUS_COMPILING = 1;
   public static final int STATUS_COMPILE_FAIL = 2;
   public static final int STATUS_COMPILE_SUCCESS = 3;

   public static final int STATUS_DOWNLOADING = 4;
   public static final int STATUS_DOWNLOAD_SUCCESS = 5;
   /**
    * 封面链接
    * The cover url
    */
   private String coverUrl;

   /**
    * 创建时间
    * The creating time
    */
   private String createdAt;

   /**
    * 任务ID
    * The job id
    */
   private String jobId;

   private String id;

   /**
    * 描述信息
    * description
    */
   private String description;

   /**
    * 标题
    * title
    */
   private String title;

   /**
    * 工程Id
    * project id
    */
   private String projectId;

   /**
    * 工程链接
    * The project url
    */
   private String url;

   /**
    * 时长
    * duration
    */
   private String duration;

   private String durationString;

   /**
    * 文件扩展名
    * The extension
    */
   private String extension;

   private int compileProgress;


   private int downloadProgress;

   private int status;

   private String fileSize;
   private boolean needDownload;

   public String getCoverUrl() {
      return coverUrl;
   }

   public void setCoverUrl(String coverUrl) {
      this.coverUrl = coverUrl;
   }

   public String getCreatedAt() {
      return createdAt;
   }

   public void setCreatedAt(String createdAt) {
      this.createdAt = createdAt;
   }

   public String getJobId() {
      return jobId;
   }

   public void setJobId(String jobId) {
      this.jobId = jobId;
   }

   public String getDescription() {
      return description;
   }

   public void setDescription(String description) {
      this.description = description;
   }

   public String getTitle() {
      return title;
   }

   public void setTitle(String title) {
      this.title = title;
   }

   public String getProjectId() {
      return projectId;
   }

   public void setProjectId(String projectId) {
      this.projectId = projectId;
   }

   public String getUrl() {
      return url;
   }

   public void setUrl(String url) {
      this.url = url;
   }

   public String getDuration() {
      return duration;
   }

   public void setDuration(String duration) {
      this.duration = duration;
   }

   public String getExtension() {
      return extension;
   }

   public void setExtension(String extension) {
      this.extension = extension;
   }

   public int getCompileProgress() {
      return compileProgress;
   }

   public void setCompileProgress(int compileProgress) {
      this.compileProgress = compileProgress;
   }

   public int getDownloadProgress() {
      return downloadProgress;
   }

   public void setDownloadProgress(int downloadProgress) {
      this.downloadProgress = downloadProgress;
   }

   public void setStatus(int status) {
      this.status = status;
   }

   public boolean isCompiling() {
      return status == STATUS_COMPILING;
   }

   public boolean isDownloading() {
      return status == STATUS_DOWNLOADING;
   }


   public String getId() {
      return id;
   }

   public void setId(String id) {
      this.id = id;
   }

   public String getFileSize() {
      return fileSize;
   }

   public void setFileSize(String fileSize) {
      this.fileSize = fileSize;
   }

   public String getDurationString() {
      return durationString;
   }

   public void setDurationString(String durationString) {
      this.durationString = durationString;
   }

   @Override
   public boolean equals(Object o) {
      if (this == o) return true;
      if (!(o instanceof VideoCompileBean)) return false;
      VideoCompileBean bean = (VideoCompileBean) o;
      return coverUrl.equals(bean.coverUrl) && createdAt.equals(bean.createdAt) && jobId.equals(bean.jobId) && description.equals(bean.description) && title.equals(bean.title) && projectId.equals(bean.projectId) && url.equals(bean.url) && duration.equals(bean.duration) && extension.equals(bean.extension);
   }

   @Override
   public int hashCode() {
      return Objects.hash(coverUrl, createdAt, jobId, description, title, projectId, url, duration, extension);
   }

   public static VideoCompileBean create(JobInfoEntity entity){
      VideoCompileBean bean = new VideoCompileBean();
      bean.setStatus(STATUS_COMPILING);
      bean.setJobId(entity.getJobId());
      bean.setCreatedAt(entity.getCreateAt());
      bean.setCoverUrl(entity.getCoverUrl());
      bean.setTitle(entity.getTitle());
      bean.setDurationString(entity.getDuration());
      bean.setProjectId(entity.getProjectUuid());
      bean.setFileSize(entity.getFileSize());
      return bean;
   }

   public void setNeedDownload(boolean needDownload) {
      this.needDownload = needDownload;
   }

   public boolean isNeedDownload() {
      return needDownload;
   }
}
