package com.meishe.draft.db;

import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.meishe.base.utils.Utils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/17 15:29
 * @Description :数据库管理者 The database manager
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DraftDbManager {
    private DraftDatabase mDatabase;

    private DraftDbManager() {
        //注意最好在获取读取sd卡权限后再调用本方法
        //It is best to call this method after obtaining the permission to read the SD card.
        RoomDatabase.Builder<DraftDatabase> builder = Room.databaseBuilder(Utils.getApp(), DraftDatabase.class, "nv_draft_database")
                .addMigrations(mMigration1To2).addMigrations(mMigration2To3)
                .allowMainThreadQueries();
        mDatabase = builder.build();
    }

    private final static class Holder {
        private static DraftDbManager INSTANCE = new DraftDbManager();
    }

    public static DraftDbManager get() {
        return Holder.INSTANCE;
    }

    public LocalDraftDao getLocalDraftDao(){
        return mDatabase.getLocalDraftDao();
    }

    public UserDraftDao getUserDraftDao(){
        return mDatabase.getUserDraftDao();
    }

    public FileInfoDao getFileInfoDao(){
        return mDatabase.getFileInfoDao();
    }

    public ResourceDao getResourceDao(){
        return mDatabase.getResourceDao();
    }

    public ProjectDao getProjectDao(){
        return mDatabase.getProjectDao();
    }

    public JobInfoDao getJobInfoDao() {
        return mDatabase.getJobInfoDao();
    }

    private Migration mMigration1To2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            //新增表 Add Table
            database.execSQL("CREATE TABLE IF NOT EXISTS `FileInfoEntity` (`id` TEXT PRIMARY KEY NOT NULL,localPath TEXT,md5 TEXT, url TEXT,userId TEXT, m3u8CommonUrl TEXT, m3u8AlphaUrl TEXT, m3u8ReverseUrl TEXT, m3u8ReverseAlphaUrl TEXT, resourceId TEXT)");
            database.execSQL("CREATE TABLE IF NOT EXISTS `ProjectEntity` (`projectResourceId` TEXT PRIMARY KEY NOT NULL,projectId TEXT,resourceId TEXT)");
            database.execSQL("CREATE TABLE IF NOT EXISTS `ResourceEntity` (`id` TEXT PRIMARY KEY NOT NULL,filePath TEXT,remotePath TEXT, leftChannelUrl TEXT,rightChannelUrl TEXT, fileName TEXT, fileNameZh TEXT, customDisPlayName TEXT, duration INTEGER NOT NULL, width INTEGER NOT NULL, height INTEGER NOT NULL, category INTEGER NOT NULL,type INTEGER NOT NULL, kind INTEGER NOT NULL, isAssets INTEGER NOT NULL, urlPrefix TEXT, interval INTEGER NOT NULL, extension TEXT)");

            database.execSQL("CREATE TABLE IF NOT EXISTS `JobInfoEntity` (`id` TEXT PRIMARY KEY AUTOINCREMENT NOT NULL, projectUuid TEXT, jobId TEXT,coverUrl TEXT, duration TEXT, createAt TEXT, title TEXT, fileSize TEXT)");
            //新增字段 Add Fields
            database.execSQL("ALTER TABLE LocalDraftEntity Add COLUMN infoJsonPath TEXT");
            database.execSQL("ALTER TABLE LocalDraftEntity Add COLUMN cloudToLocalMapInfo TEXT");
            database.execSQL("ALTER TABLE LocalDraftEntity Add COLUMN templatePath TEXT");
            database.execSQL("ALTER TABLE LocalDraftEntity Add COLUMN cloudModifiedAt TEXT");
        }
    };

    private Migration mMigration2To3 = new Migration(2, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            //新增字段 Add Fields
            database.execSQL("ALTER TABLE LocalDraftEntity Add COLUMN remoteId TEXT");
            database.execSQL("ALTER TABLE ResourceEntity Add COLUMN realId TEXT");
            database.execSQL("ALTER TABLE FileInfoEntity Add COLUMN localReversePath TEXT");
        }
    };
}
