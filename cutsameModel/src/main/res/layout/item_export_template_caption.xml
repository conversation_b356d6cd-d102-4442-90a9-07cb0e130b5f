<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_marginTop="@dimen/dp_px_39"
    android:layout_height="@dimen/dp_px_159"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/ll_image_container"
        android:layout_width="@dimen/dp_px_159"
        android:layout_height="match_parent"
        android:orientation="horizontal">
        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:scaleType="fitXY"
            />
        <ImageView
            android:id="@+id/iv_lock"
            android:layout_width="@dimen/dp_px_30"
            android:layout_height="@dimen/dp_px_36"
            android:layout_centerInParent="true"
            android:background="@mipmap/ic_cut_same_export_template_locked_gray"
            />

    </RelativeLayout>

    <TextView
        android:id="@+id/tv_caption_name"
        android:layout_toRightOf="@+id/ll_image_container"
        android:layout_toLeftOf="@+id/iv_is_lock"
        android:layout_marginRight="@dimen/dp_px_60"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:singleLine="true"
        android:ellipsize="end"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30"
        />

    <TextView
        android:id="@+id/tv_clip_duration"
        android:layout_toRightOf="@+id/ll_image_container"
        android:layout_below="@+id/tv_caption_name"
        android:layout_marginTop="@dimen/dp_px_18"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_ff707070"
        android:textSize="@dimen/sp_px_27"
        />



    <ImageView
        android:id="@+id/iv_is_lock"
        android:layout_width="@dimen/dp_px_36"
        android:layout_height="@dimen/dp_px_45"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/dp_px_72"
        android:layout_centerVertical="true"
        />

    <ImageView
        android:id="@+id/iv_select_caption"
        android:layout_width="@dimen/dp_px_36"
        android:layout_height="@dimen/dp_px_36"
        android:layout_alignParentRight="true"
        android:layout_marginRight="@dimen/dp_px_72"
        android:layout_centerVertical="true"
        android:scaleType="fitXY"
        />

</RelativeLayout>