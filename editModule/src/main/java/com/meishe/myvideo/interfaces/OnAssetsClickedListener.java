package com.meishe.myvideo.interfaces;

import com.meishe.engine.interf.IBaseInfo;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/5/19 21:00
 * @Description :资源点击回调 On Assets Clicked Listener
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface OnAssetsClickedListener {
    /**
     * On item clicked.
     *
     * @param baseInfo the base info
     */
    void onItemClicked(IBaseInfo baseInfo);
}
