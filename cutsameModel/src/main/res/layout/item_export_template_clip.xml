<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginTop="@dimen/dp_px_39"
    android:layout_marginBottom="@dimen/dp_px_12">

    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintTop_toBottomOf="@+id/iv_image"
        android:layout_marginTop="@dimen/dp_px_36"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dp_px_30"
            android:textColor="@color/white_8"
            android:gravity="center"
            android:id="@+id/tv_title_trim_in"
            android:text="@string/cut_same_clip_trim_in"/>
        <TextView
            android:paddingTop="@dimen/dp_px_3"
            android:paddingBottom="@dimen/dp_px_3"
            android:paddingStart="@dimen/dp_px_21"
            android:paddingEnd="@dimen/dp_px_21"
            android:id="@+id/tv_trim_in"
            android:background="@color/color_ff333333"
            app:layout_constraintLeft_toRightOf="@+id/tv_title_trim_in"
            android:layout_marginStart="@dimen/dp_px_24"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textSize="@dimen/dp_px_30"
            android:textColor="@color/white_8"
            android:text="01:02:03:04"/>

        <TextView
            android:id="@+id/tv_title_trim_out"
            app:layout_constraintLeft_toRightOf="@+id/tv_trim_in"
            android:layout_marginStart="@dimen/dp_px_120"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="@dimen/dp_px_30"
            android:textColor="@color/white_8"
            android:text="@string/cut_same_clip_trim_out"/>

        <TextView
            android:paddingTop="@dimen/dp_px_3"
            android:paddingBottom="@dimen/dp_px_3"
            android:paddingStart="@dimen/dp_px_21"
            android:paddingEnd="@dimen/dp_px_21"
            android:gravity="center"
            android:id="@+id/tv_trim_out"
            android:layout_marginStart="@dimen/dp_px_24"
            android:padding="@dimen/dp_px_3"
            app:layout_constraintLeft_toRightOf="@+id/tv_title_trim_out"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dp_px_30"
            android:textColor="@color/white_8"
            android:text="00:00:00:00"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/iv_image"
        android:scaleType="centerCrop"
        android:layout_width="@dimen/dp_px_159"
        android:layout_height="@dimen/dp_px_159"
        tools:ignore="ContentDescription" />

    <ImageView
        android:id="@+id/iv_bg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="@dimen/dp_px_159"
        android:layout_height="@dimen/dp_px_159"
        android:background="@mipmap/ic_cut_same_export_template_clip_select"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/iv_mont_layer"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="@dimen/dp_px_159"
        android:layout_height="@dimen/dp_px_159"
        android:background="@color/black_99" />

    <ImageView
        android:id="@+id/iv_lock"
        app:layout_constraintLeft_toLeftOf="@+id/iv_image"
        app:layout_constraintRight_toRightOf="@+id/iv_image"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        app:layout_constraintTop_toTopOf="@+id/iv_image"
        android:layout_width="@dimen/dp_px_36"
        android:layout_height="@dimen/dp_px_45"
        android:layout_centerInParent="true"
        android:background="@mipmap/ic_cut_same_export_template_locked_gray"
        tools:ignore="ContentDescription" />

    <TextView
        app:layout_constraintLeft_toRightOf="@+id/iv_image"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/tv_clip_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_30"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30"
        tools:ignore="RelativeOverlap" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/tv_clip_name"
        app:layout_constraintLeft_toRightOf="@+id/iv_image"
        android:id="@+id/tv_clip_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginTop="@dimen/dp_px_18"
        android:textColor="@color/color_ff707070"
        android:textSize="@dimen/sp_px_27" />


    <TextView
        app:layout_constraintLeft_toRightOf="@+id/iv_image"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        android:id="@+id/tv_footage_type"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_30"
        android:layout_marginLeft="@dimen/dp_px_30"
        android:drawableEnd="@mipmap/ic_cut_same_export_template_array_down"
        android:drawablePadding="@dimen/dp_px_15"
        android:paddingTop="@dimen/dp_px_15"
        android:paddingBottom="@dimen/dp_px_15"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_27" />

    <FrameLayout
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_image"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        android:id="@+id/frame_is_lock"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_12"
        android:paddingLeft="@dimen/dp_px_60"
        android:paddingRight="@dimen/dp_px_60">

        <ImageView
            android:id="@+id/iv_is_lock"
            android:layout_width="@dimen/dp_px_36"
            android:layout_height="@dimen/dp_px_45"
            android:layout_gravity="center"
            android:scaleType="fitXY" />
    </FrameLayout>

    <FrameLayout
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv_image"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        android:id="@+id/frame_group"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginEnd="@dimen/dp_px_12"
        android:paddingLeft="@dimen/dp_px_60"
        android:paddingRight="@dimen/dp_px_60">

        <ImageView
            android:id="@+id/iv_group"
            android:layout_width="@dimen/dp_px_36"
            android:layout_height="@dimen/dp_px_36"
            android:layout_gravity="center"
            android:scaleType="fitXY" />
    </FrameLayout>

    <TextView
        app:layout_constraintTop_toTopOf="@+id/iv_image"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        app:layout_constraintRight_toLeftOf="@+id/rl_footage_container"
        android:id="@+id/tv_toggle_voice"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/dp_px_78"
        android:layout_marginEnd="@dimen/dp_px_20"
        android:background="@color/black"
        android:gravity="center"
        android:paddingStart="@dimen/dp_px_15"
        android:paddingEnd="@dimen/dp_px_15"
        android:text="@string/no_voice"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30"
        tools:ignore="RelativeOverlap" />

    <RelativeLayout
        app:layout_constraintTop_toTopOf="@+id/iv_image"
        app:layout_constraintBottom_toBottomOf="@+id/iv_image"
        app:layout_constraintRight_toLeftOf="@+id/frame_is_lock"
        android:id="@+id/rl_footage_container"
        android:layout_width="@dimen/dp_px_180"
        android:layout_height="@dimen/dp_px_78"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/dp_px_100"
        android:background="@color/black">

        <TextView
            android:id="@+id/tv_groups"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp_px_40"
            android:textColor="@color/white"
            android:textSize="@dimen/sp_px_30" />

        <ImageView
            android:id="@+id/iv_group_down"
            android:layout_width="@dimen/dp_px_18"
            android:layout_height="@dimen/dp_px_18"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/dp_px_12"
            android:layout_toEndOf="@+id/tv_groups"
            android:background="@mipmap/ic_cut_same_export_template_array_down"
            android:contentDescription="@null" />
    </RelativeLayout>
</androidx.constraintlayout.widget.ConstraintLayout>