package com.meishe.speaker.bean;

import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.meishe.base.utils.Utils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import static com.meishe.engine.bean.CommonData.DEFAULT_LENGTH;
import static com.meishe.engine.bean.CommonData.MIN_SHOW_LENGTH_DURATION;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/1 15:40
 * @Description :讯飞语音听写结果实体类 IFlytek Voice dictation result entity class
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class Speech implements Serializable {
    private final int MAX_TEXT_NUM = 13;
    /**
     * 句子顺序
     * Sentence order
     */
    private int sn;
    /**
     * true代表最后一句
     * True represents the last sentence
     */
    private boolean ls;
    /**
     * 开始时间
     * time-on
     */
    private int bg;
    private int ed;
    /**
     * 词
     * words
     */
    private List<Words> ws;

    public int getSn() {
        return sn;
    }

    public void setSn(int sn) {
        this.sn = sn;
    }

    public boolean isLs() {
        return ls;
    }

    public void setLs(boolean ls) {
        this.ls = ls;
    }

    public int getBg() {
        return bg;
    }

    public void setBg(int bg) {
        this.bg = bg;
    }

    public int getEd() {
        return ed;
    }

    public void setEd(int ed) {
        this.ed = ed;
    }

    public List<Words> getWs() {
        return ws;
    }

    public void setWs(List<Words> ws) {
        this.ws = ws;
    }

    /**
     * 获取文字信息列表，主要根据标点符号断句，如果语句时长 > DEFAULT_LENGTH，则自动断句。
     * Get text information list, mainly according to the punctuation mark sentence break, if the statement duration > DEFAULT LENGTH, it will automatically break the sentence
     */
    public List<Text> getText() {
        if (ws != null) {
            List<Text> list = new ArrayList<>();
            Text text = null;
            long lastBelongInPoint = 0;
            for (int i = 0; i < ws.size(); i++) {
                Words words = ws.get(i);
                if (words.cw != null) {
                    String w = words.cw.get(0).w;
                    //取第一个，后边的属于候选，忽略之。
                    // Take the first one, and ignore the ones that follow as candidates.
                    if ((i == 0 && isPunctuation(w)) || TextUtils.isEmpty(w)) {
                        /*
                         * 如果第一个是标点符号或者为空，继续
                         * If the first is punctuation or empty, continue
                         */
                        continue;
                    }
                    if (text == null || words.getBelongInPoint() != lastBelongInPoint) {
                        //第一次text == null，或者视频/语音片段归属入点不同就断句
                        // The first time text==null, or if the video/voice segment belongs to a different entry point, the sentence will be broken/
                        list.add(text = createText(words));
                        lastBelongInPoint = words.getBelongInPoint();
                    }
                    text.appendText(w);
                    if (words.bg > 0) {
                        text.interval = words.bg * 10 - text.bg;
                    }
                    //LogUtils.d("text.text=" + text + "/text.length()=" + text.text.length() + "/words=" + words);
                    if (isPunctuation(w) && i < ws.size() - 1) {
                        /*
                         * 先以标点作为主要断句依据
                         * First, take punctuation as the main sentence breaking basis
                         */
                        Words nextWords = ws.get(i + 1);
                        if (nextWords != null && nextWords.bg * 10 - (text.getBg() + text.interval)
                                >= MIN_SHOW_LENGTH_DURATION / 1000) {
                            //和下个字的间隔>MIN_SHOW_LENGTH_DURATION/1000,可以断句了,防止语句过短
                            // Interval between next word>MIN_ SHOW_ LENGTH_ DUration/1000,
                            // can be broken to prevent short sentences.
                            if (text.interval < MIN_SHOW_LENGTH_DURATION / 1000) {
                                text.interval = MIN_SHOW_LENGTH_DURATION / 1000;
                            }
                            list.add(text = createText(nextWords));
                            continue;
                        }
                        //如果时长达标，则创建下一个text，否则等下一个标点
                        // If the time is long, create the next text, otherwise wait for the next punctuation.
                        if (text.interval * 1000 >= MIN_SHOW_LENGTH_DURATION) {
                            list.add(text = createText(words));
                        }
                    } else if (i == ws.size() - 1) {
                        /*
                         * 检测最后一段的间隔（有可能为负值）。
                         * Detect the interval of the last segment (possibly negative value).
                         */
                        if (text.interval < MIN_SHOW_LENGTH_DURATION / 1000) {
                            text.interval = MIN_SHOW_LENGTH_DURATION / 1000;
                        }
                    } else {
                        Words nextWords = ws.get(i + 1);
                        String nextText = "";
                        if (nextWords != null && nextWords.getCw() != null
                                && isPunctuation(nextText = nextWords.getCw().get(0).w)) {
                            /*
                             *下一个是标点则继续。
                             * The next is punctuation and continues.
                             */
                            continue;
                        }
                        /*
                         *  如果下一个词属于新的片段，现在的text可能只有一个词，需要修正时间间隔
                         *  If the next word belongs to a new segment,
                         *  the current text may only have one word, and the time interval needs to be corrected
                         */
                        if (nextText != null && nextWords.belongInPoint != words.belongInPoint) {
                            if (text.interval < MIN_SHOW_LENGTH_DURATION / 1000) {
                                text.interval = MIN_SHOW_LENGTH_DURATION / 1000;
                            }
                        }
                        int length = text.text.length();
                        if(!TextUtils.isEmpty(nextText) && length + nextText.length() > MAX_TEXT_NUM){
                            /*
                             * 如果加上下一个段文字，大于13个字,断句
                             * If the next paragraph is added, more than 13 words, break the sentence
                             */
                            list.add(text = createText(words));
                            continue;
                        }
                        /*
                         * 如果字数大于等于13或者大于默认长度截断之，注意：先判断字数再判断时长
                         * If the number of words is greater than or equal to 13 or greater than
                         * the default length of truncation, note: judge the number of words
                         * first and then the duration
                         */
                        if (length >= MAX_TEXT_NUM || text.interval >= DEFAULT_LENGTH / 1000) {
                            list.add(text = createText(words));
                        }
                    }
                }
            }
            return list;
        }
        return null;
    }

    /**
     * 创建语句
     * Create text
     */
    private Text createText(Words words) {
        Text text = new Text();
        text.bg = words.bg * 10;
        //每段第一个默认间隔 The first default interval of each segment.
        text.interval = MIN_SHOW_LENGTH_DURATION / 1000;
        return text;
    }

    /**
     * 是否是标点符号
     * Is it punctuation?
     */
    public boolean isPunctuation(String word) {
        if (Utils.isZh()) {
            return "，".equals(word) || ",".equals(word) || "。".equals(word) || "？".equals(word);
        } else {
            return ",".equals(word) || ".".equals(word) || "?".equals(word);
        }
    }

    public boolean isPunctuation(Word word) {
        if (word == null) {
            return false;
        }
        return isPunctuation(word.w);
    }

    public static class Words implements Serializable {
        /**
         * 开始时间
         * begin time
         */
        private int bg;
        /**
         * 中文分词
         * Chinese word
         */
        private List<Word> cw;

        /**
         * 自定义属性字段，所属于视频/语音片段的入点的。
         * Custom attribute fields that belong to the entry point of the video/voice segment.
         */
        private long belongInPoint;

        public int getBg() {
            return bg;
        }

        public void setBg(int bg) {
            this.bg = bg;
        }

        public List<Word> getCw() {
            return cw;
        }

        public void setCw(List<Word> cw) {
            this.cw = cw;
        }

        public boolean isEmpty() {
            return cw == null || cw.get(0) == null || TextUtils.isEmpty(cw.get(0).w);
        }

        public long getBelongInPoint() {
            return belongInPoint;
        }

        public void setBelongInPoint(long belongInPoint) {
            this.belongInPoint = belongInPoint;
        }

        @NonNull
        @Override
        public String toString() {
            return "Words(bg=" + bg + ",cw1=" + (cw == null ? "" : cw.get(0).toString()) + ",belongInPoint=" + belongInPoint + ")";
        }
    }

    public static class Word implements Serializable {
        private float sc;//分数 fraction
        private String w;//字 character

        public float getSc() {
            return sc;
        }

        public void setSc(float sc) {
            this.sc = sc;
        }

        public String getW() {
            return w;
        }

        public void setW(String w) {
            this.w = w;
        }


        @NonNull
        @Override
        public String toString() {
            return "Word(sc=" + sc + ",w=" + w + ")";
        }
    }

    /**
     * 自定义文字信息
     * Customize text messages
     */
    public static class Text implements Serializable {
        /**
         * 文字
         * Text
         */
        private StringBuilder text = new StringBuilder();
        /**
         * 开始时间
         * The begin time
         */
        private long bg;

        /**
         * 间隔时间
         * The interval
         */
        private long interval;

        public String getText() {
            return text.toString();
        }

        public void appendText(String text) {
            this.text.append(text);
        }

        public long getBg() {
            return bg;
        }

        public void setBg(long bg) {
            this.bg = bg;
        }

        public long getInterval() {
            return interval;
        }

        public long getDuration() {
            return bg + interval;
        }

        public void setInterval(long interval) {
            this.interval = interval;
        }

        @NonNull
        @Override
        public String toString() {
            return "Text(bg=" + bg + ",interval=" + interval + ",w=" + text.toString() + ")";
        }
    }
}
