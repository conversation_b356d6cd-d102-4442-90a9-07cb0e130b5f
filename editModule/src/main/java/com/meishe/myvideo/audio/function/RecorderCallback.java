package com.meishe.myvideo.audio.function;

/**
 * The interface Recorder callback.
 * 此接口为录音的回调
 */
public interface RecorderCallback {
    /**
     * RecorderStart
     * 录音开始
     *
     * @return the boolean
     */
    boolean onRecorderStart();

    /**
     * RecorderStop
     * 录音停止
     */
    void onRecorderStop();

    /**
     * onRecorded
     * 正在录音
     *
     * @param wave 录制的数据data
     */
    void onRecorded(short[] wave);


    /**
     * RecordedFail
     * 录制失败
     *
     * @param paramInt 失败的code
     */
    void onRecordedFail(int paramInt);
}
