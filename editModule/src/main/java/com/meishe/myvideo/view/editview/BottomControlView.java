package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.graphics.Color;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import com.meishe.myvideo.R;


/**
 * The type Bottom control view.
 * 底层控制视图类
 */
public class BottomControlView extends EditControlView implements EditView.OnClosedListener {

    public BottomControlView(Context context) {
        super(context);
        setBackgroundColor(Color.RED);
    }

    public BottomControlView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void initView() {
    }

    /**
     * Show.
     * 显示
     * @param view the view
     */
    public void show(EditView view) {
        view.setOnClosedListener(this);
        showDialogView(view);
    }

    @Override
    public void dismiss() {
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.view_exit);
        this.setAnimation(animation);
        this.setVisibility(GONE);
    }

    /**
     * Show dialog view.
     * 显示对话框视图
     * @param view the view
     */
    public void showDialogView(View view) {
        this.removeAllViews();
        this.addView(view);
        Animation animation = AnimationUtils.loadAnimation(mContext, R.anim.view_enter);
        this.setAnimation(animation);
        this.setVisibility(VISIBLE);
    }

    @Override
    public void onClosed() {
        dismiss();
    }
}
