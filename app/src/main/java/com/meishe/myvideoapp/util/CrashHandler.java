package com.meishe.myvideoapp.util;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Log;

import com.meicam.sdk.NvsRational;
import com.meishe.base.manager.AppManager;
import com.meishe.base.utils.LogUtils;
import com.meishe.draft.DraftManager;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.MeicamTimeline;
import com.meishe.engine.util.PathUtils;
import com.meishe.myvideoapp.R;
import com.meishe.myvideo.edit.manager.EditOperateManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.io.Writer;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.meishe.logic.constant.PagerConstants.FROM_MAIN_PAGE;

/**
 * 全局异常捕获
 * <p>
 * Global exception catch
 * Created by ms on 2018/9/18.
 */
public class CrashHandler implements Thread.UncaughtExceptionHandler {

    private Thread.UncaughtExceptionHandler mDefaultHandler;
    private Context mContext;
    private String TAG = this.getClass().getSimpleName();
    /*
     * 存储异常和参数信息
     * Store exception and parameter information
     * */
    private Map<String, String> paramsMap = new HashMap<>();
    private static CrashHandler mInstance;

    private CrashHandler() {

    }

    public static synchronized CrashHandler getInstance() {
        if (null == mInstance) {
            mInstance = new CrashHandler();
        }
        return mInstance;
    }

    public void init(Context context) {
        mContext = context;
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }

    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        if (!handleException(thread, ex) && mDefaultHandler != null) {
            mDefaultHandler.uncaughtException(thread, ex);
        } else {
            /**
             * 清理所有activity，避免5.0以下版本重启引发的未知问题
             */
            AppManager.getInstance().finishAllActivity();
            android.os.Process.killProcess(android.os.Process.myPid());
            System.exit(0);
        }
    }

    private boolean handleException(Thread thread, Throwable ex) {
        if (ex == null) {
            return false;
        }
        saveDraft();
        paramsMap.put("thread name", thread.getName());

        /*
         * 收集设备参数信息
         * Collect device parameter information
         * */
        collectDeviceInfo(mContext);

        /*
         * 保存日志文件
         * Save log file
         * */
        saveCrashInfo2File(ex);
        return true;
    }

    /**
     * 保存草稿
     * save draft
     */
    public void saveDraft() {
        try {
            EditorEngine editorEngine = EditorEngine.getInstance();
            MeicamTimeline timeline = editorEngine.getCurrentTimeline();
            if (timeline == null) {
                LogUtils.e("timeline is null");
                return;
            }
            Bitmap bitmap = null;
            if (TextUtils.isEmpty(timeline.getCoverImagePath())) {
                bitmap = EditorEngine.getInstance().grabImageFromTimeline(timeline, 0, new NvsRational(1, 1));
            }
            int fromType = AppManager.getInstance().getFromType();
            if (fromType == FROM_MAIN_PAGE) {
                //从草稿页面进入。
                if (EditOperateManager.get().haveOperate()) {
                    //有操作
                    DraftManager.getInstance().updateDraft(timeline,timeline.getDuration(), bitmap, true);
                } else {
                    //无操作
                    DraftManager.getInstance().updateDraft(timeline,timeline.getDuration(), bitmap, false);
                }
            } else if (fromType >= 0){
                //从非草稿页面进入，即草稿尚未创建。
                DraftManager.getInstance().saveDraft(timeline,timeline.getDuration(), bitmap);
            }
            AppManager.getInstance().setFromType(-1);
        } catch (Exception e) {
            LogUtils.e("saveDraft error: " + e.getMessage());
        }

    }

    /*
     *  收集设备参数信息
     * Collect device parameter information
     * */
    public void collectDeviceInfo(Context ctx) {
        //获取versionName,versionCode
        try {
            PackageManager pm = ctx.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null) {
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + "";
                paramsMap.put("versionName", versionName);
                paramsMap.put("versionCode", versionCode);
            }
        } catch (PackageManager.NameNotFoundException e) {
            LogUtils.e("collectDeviceInfo: an error occured when collect package info", e);
        }
        /*
         * 获取所有系统信息
         * Get all system information
         * */
        Field[] fields = Build.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                paramsMap.put(field.getName(), field.get(null).toString());
            } catch (Exception e) {
                LogUtils.e("collectDeviceInfo: an error occured when collect crash info", e);
            }
        }
    }

    private String saveCrashInfo2File(Throwable ex) {
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sb.append(key + "=" + value + "\n");
        }

        Writer writer = new StringWriter();
        PrintWriter printWriter = new PrintWriter(writer);
        ex.printStackTrace(printWriter);
        Throwable cause = ex.getCause();
        while (cause != null) {
            cause.printStackTrace(printWriter);
            cause = cause.getCause();
        }
        printWriter.close();
        String result = writer.toString();
        sb.append(result);
        try {
            long timestamp = System.currentTimeMillis();

            SimpleDateFormat yyFormat = new SimpleDateFormat(mContext.getString(R.string.simple_data_format_yy_mm_dd_hh_ss));
            String time = yyFormat.format(new Date());
            String fileName = "crash-" + time + "-" + timestamp + ".log";
            if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
                String path = PathUtils.getLogDir() + File.separator;
                Log.d(TAG, "saveCrashInfo2File: " + path);
                File dir = new File(path);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                FileOutputStream fos = new FileOutputStream(path + fileName);
                fos.write(sb.toString().getBytes());
                fos.close();
            }
            return fileName;
        } catch (Exception e) {
            LogUtils.e("saveCrashInfo2File: an error occured while writing file...", e);
        }
        return null;
    }
}

