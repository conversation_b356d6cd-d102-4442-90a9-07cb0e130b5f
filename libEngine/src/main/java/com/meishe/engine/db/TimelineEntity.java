package com.meishe.engine.db;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/5/17 10.50
 * @Description :时间线数据类 timeline data
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
@Entity
public class TimelineEntity {
    /**
     * 主键，时间线资源Id
     * The primary key  , timeline data id
     */
    @PrimaryKey
    @NonNull
    private String id = "123";
    /**
     * 时间线json数据
     * json data
     */
    private String json;

    public String getId() {
        return id;
    }

    public void setId(@NonNull String id) {
        this.id = id;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    @NonNull
    @Override
    public String toString() {
        return "{id=" + id + ",json=" + json + "}";
    }
}
