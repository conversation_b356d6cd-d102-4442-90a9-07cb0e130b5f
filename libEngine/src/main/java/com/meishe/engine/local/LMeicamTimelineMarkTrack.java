package com.meishe.engine.local;

import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by Cao<PERSON>hi<PERSON>hao on 2020/7/3 20:36
 */
public class LMeicamTimelineVideoFxTrack extends LTrackInfo implements Cloneable, Serializable {
    @SerializedName("clipInfos")
    private List<LMeicamTimelineVideoFxClip> mClipInfoList = new ArrayList<>();

    /**
     * 滤镜和调节所对应的字段
     */
    @SerializedName("filterAndAdjustClips")
    protected List<LMeicamTimelineVideoFilterAndAdjustClip> filterAndAdjustClips = new ArrayList<>();


    public LMeicamTimelineVideoFxTrack(int index) {
        super(CommonData.TRACK_TIMELINE_FX, index);
    }

    @NonNull
    @Override
    public LMeicamTimelineVideoFxTrack clone() {
        return (LMeicamTimelineVideoFxTrack) DeepCopyUtil.deepClone(this);
    }


    public List<LMeicamTimelineVideoFxClip> getClipInfoList() {
        return mClipInfoList;
    }


    public List<LMeicamTimelineVideoFilterAndAdjustClip> getFilterAndAdjustClips() {
        return filterAndAdjustClips;
    }

    public void setFilterAndAdjustClips(List<LMeicamTimelineVideoFilterAndAdjustClip> filterAndAdjustClips) {
        this.filterAndAdjustClips = filterAndAdjustClips;
    }
}
