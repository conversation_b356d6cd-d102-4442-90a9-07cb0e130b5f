package com.meishe.engine.bean;


import com.meicam.sdk.NvsVideoFx;
import com.meishe.engine.local.LMeicamTimelineVideoFx;
import com.meishe.engine.local.LMeicamVideoFx;

/**
 * author：yangtailin on 2020/8/13 10:52
 */
public class MeicamTimelineVideoFx extends MeicamVideoFx {


    public MeicamTimelineVideoFx(NvsVideoFx videoFx, String type, String subType, String desc) {
        super(videoFx, type, subType, desc);
        classType = "timelineVideoFx";
    }


    @Override
    public void setIntensity(float intensity) {
        super.intensity = intensity;
    }

    @Override
    public LMeicamTimelineVideoFx parseToLocalData() {
        LMeicamTimelineVideoFx local = new LMeicamTimelineVideoFx();
        setCommonData(local);
        local.setInPoint(getInPoint());
        local.setOutPoint(getOutPoint());
        return local;
    }

    @Override
    public void recoverFromLocalData(LMeicamVideoFx local) {
        super.recoverFromLocalData(local);
        setCommonRecoverData(local);
        setInPoint(((LMeicamTimelineVideoFx) local).getInPoint());
        setOutPoint(((LMeicamTimelineVideoFx) local).getOutPoint());
    }
}
