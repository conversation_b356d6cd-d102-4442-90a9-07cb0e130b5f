package com.meishe.myvideo.fragment.adapter;

import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import com.meishe.base.bean.MediaData;
import com.meishe.base.bean.MediaSection;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseSectionQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/10 19:47
 * @Description :本地素材选择适配器 Material select Adapter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class BaseSelectAdapter extends BaseSectionQuickAdapter<MediaSection, BaseViewHolder> {
    private final int mItemSize;
    private int mSelectedPos;

    public BaseSelectAdapter(int layoutResId, int itemSize) {
        super(layoutResId, R.layout.item_header_material_select, null);
        this.mItemSize = itemSize;
    }

    @NonNull
    @Override
    public BaseViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        BaseViewHolder holder = super.onCreateViewHolder(parent, viewType);
        //BaseSectionQuickAdapter这里是0,其他的不是
        // BaseSectionQuickAdapter here is 0, others are not
        if (viewType == 0 && holder.itemView.getHeight() != mItemSize) {
            //已经设置过就不再设置了
            // It has been set and will no longer be set
            setLayoutParams(holder.itemView);
        }
        addChildClick(holder);
        return holder;
    }

    public void addChildClick(BaseViewHolder holder) {

    }

    private void setLayoutParams(View view) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        if (layoutParams == null) {
            layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        }
        // layoutParams.width = mItemSize;
        layoutParams.height = mItemSize;
        view.setLayoutParams(layoutParams);
    }

    /**
     * 选中某一项，一般用于单选(互斥)
     * Select an item, usually for single selection
     *
     * @param position The selected position
     */
    public void select(int position,MediaData selectedMedia) {
        if (position >= 0 && position < getData().size()) {
            if (mSelectedPos >= 0 && position != mSelectedPos) {
                MediaSection lastSelectedMedia = getItem(mSelectedPos);
                if(lastSelectedMedia != null && !lastSelectedMedia.isHeader){
                    lastSelectedMedia.t.setState(!lastSelectedMedia.t.isState());
                }
                notifyItemChanged(mSelectedPos);
            }
            selectedMedia.setState(!selectedMedia.isState());
            if(mSelectedPos == position){
                //再次选中则取消
                // Check again to cancel
                mSelectedPos = -1;
            }else{
                mSelectedPos = position;
            }
            notifyItemChanged(position);
        }
    }

    @Override
    protected void convertHead(BaseViewHolder helper, MediaSection section) {
        helper.setText(R.id.tv_date, section.header);
    }
}
