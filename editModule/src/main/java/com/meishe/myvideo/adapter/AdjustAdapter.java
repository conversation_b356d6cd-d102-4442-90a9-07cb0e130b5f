package com.meishe.myvideo.adapter;

import android.widget.CheckBox;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.third.adpater.BaseViewHolder;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2020/12/29 13:50
 * @Description :调节菜单适配器 Adapter for adjust menu.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class AdjustAdapter extends BaseSelectAdapter<IBaseInfo> {
    public AdjustAdapter() {
        super(R.layout.view_adjust_item);
    }

    @Override
    protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
        CheckBox icon = helper.getView(R.id.icon);
        TextView name = helper.getView(R.id.name);
        icon.setBackgroundResource(item.getCoverId());
        icon.setClickable(false);
        name.setText(item.getName());
        if (getSelectPosition() == helper.getAdapterPosition()) {
            icon.setChecked(true);
            name.setTextColor(mContext.getResources().getColor(R.color.adjust_selected_bg));
        } else {
            icon.setChecked(false);
            name.setTextColor(mContext.getResources().getColor(R.color.white_8));
        }
    }
}
