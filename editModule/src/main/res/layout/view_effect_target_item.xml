<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/root_cover"
        android:layout_width="@dimen/dp_px_150"
        android:layout_height="@dimen/dp_px_150">
        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@color/color_ff313233"
            android:contentDescription="@null" />
        <com.meishe.base.view.RoundBoundView
            android:id="@+id/iv_cover_round"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:boundInnerRadius="@dimen/dp_px_6"
            app:boundColor="@color/color_ff252525"/>
    </FrameLayout>
    <TextView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/root_cover"
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_12"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_30" />
</androidx.constraintlayout.widget.ConstraintLayout>