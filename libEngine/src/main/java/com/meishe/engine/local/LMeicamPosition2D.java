package com.meishe.engine.local;

import androidx.annotation.NonNull;

import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/5/13 16:59
 * @Description:
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public class LMeicamPosition2D implements Serializable, Cloneable {
    public float x;
    public float y;

    public LMeicamPosition2D(float var1, float var2) {
        this.x = var1;
        this.y = var2;
    }

    @NonNull
    @Override
    public LMeicamPosition2D clone() {
        return (LMeicamPosition2D) DeepCopyUtil.deepClone(this);
    }
}
