package com.meishe.myvideo.activity;

import android.content.Intent;
import android.graphics.Point;
import android.os.Bundle;
import android.widget.SeekBar;

import androidx.fragment.app.FragmentManager;

import com.meishe.base.manager.AppManager;
import com.meishe.base.model.BaseMvpActivity;
import com.meishe.engine.bean.CutData;
import com.meishe.engine.util.StoryboardUtil;
import com.meishe.myvideo.R;
import com.meishe.myvideo.activity.iview.ClipCuttingView;
import com.meishe.myvideo.activity.presenter.ClipCuttingPresenter;
import com.meishe.myvideo.view.CuttingMenuView;
import com.meishe.myvideo.view.MYSeekBarView;
import com.meishe.player.view.CutVideoFragment;
import com.meishe.player.view.cutregion.BaseCutRegionFragment;
import com.meishe.player.view.cutregion.ICutRegionFragment;


/**
 * The type Clip cutting activity.
 * 此类为 剪辑切割
 */
public class ClipCuttingActivity extends BaseMvpActivity<ClipCuttingPresenter> implements ClipCuttingView {
    /**
     * The constant INTENT_KEY_TIMELINE_HEIGHT.
     * 通过bundle传值的key 时间线的高度
     */
    public final static String INTENT_KEY_TIMELINE_HEIGHT = "timeline_height";
    /**
     * The constant INTENT_KEY_TIMELINE_WIDTH.
     * 时间线的宽度
     */
    public final static String INTENT_KEY_TIMELINE_WIDTH = "timeline_width";
    /**
     * The constant INTENT_KEY_TRACK_INDEX.
     * 轨道的索引
     */
    public final static String INTENT_KEY_TRACK_INDEX = "track_index";
    /**
     * 片段索引\
     * Video clip index
     */
    public final static String CLIP_INDEX = "clip_index";
    private BaseCutRegionFragment mVideoFragment;
    private CuttingMenuView mCuttingMenuView;

    private void initListener() {
        mCuttingMenuView.setOnSeekBarListener(new MYSeekBarView.OnSeekBarListener() {
            @Override
            public void onStopTrackingTouch(int progress, String name) {
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {

            }

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    mVideoFragment.rotateVideo(progress - 45);
                }

            }
        });
        mCuttingMenuView.setOnRatioSelectListener(new CuttingMenuView.OnRatioSelectListener() {
            @Override
            public void onItemClicked(int ratio) {
                mVideoFragment.changeCutRectView(ratio);
            }

            @Override
            public void onReset() {
                mVideoFragment.reset();
                mCuttingMenuView.setProgress(0);
            }
        });
        mCuttingMenuView.setOnConfrimListener(new CuttingMenuView.OnConfirmListener() {
            @Override
            public void onConfirm() {
                mPresenter.addCutEffect();
            }
        });
    }


    private void updatePlaySeekBar() {//定位预览视频图像
        mVideoFragment.seekTimeline(0, 0);
    }

    @Override
    protected int bindLayout() {
        return R.layout.activity_clip_cutting;
    }

    @Override
    protected void initData(Bundle savedInstanceState) {
        Intent intent = getIntent();
        int originalTimelineHeight = intent.getIntExtra(INTENT_KEY_TIMELINE_HEIGHT, 0);
        int originalTimelineWidth = intent.getIntExtra(INTENT_KEY_TIMELINE_WIDTH, 0);
        int trackIndex = intent.getIntExtra(INTENT_KEY_TRACK_INDEX, 0);
        mPresenter.intData(originalTimelineWidth, originalTimelineHeight, trackIndex,intent.getIntExtra(CLIP_INDEX, 0));
    }

    @Override
    protected void initView() {
        mCuttingMenuView = findViewById(R.id.edit_cutting_menu_view);
        CutData cutData = mPresenter.getCutData();
        if (cutData != null) {
            mCuttingMenuView.setProgress(cutData.getTransformData(StoryboardUtil.STORYBOARD_KEY_ROTATION_Z));
            mCuttingMenuView.setSelectRatio(cutData.getRatio());
        }
        initPlayer();
        initListener();
    }

    private void initPlayer() {
        if (mPresenter.getTimeline() != null) {
            FragmentManager fragmentManager = getSupportFragmentManager();
            mVideoFragment = CutVideoFragment.newInstance(0L);
            mVideoFragment.setCutData(mPresenter.getCutData());
            mVideoFragment.setTimeLine(mPresenter.getTimeline());
            delayToDealOnUiThread(new Runnable() {
                @Override
                public void run() {
                    mVideoFragment.initData();
                    updatePlaySeekBar();
                }
            });
            fragmentManager.beginTransaction().add(R.id.edit_preview_view, mVideoFragment).commitAllowingStateLoss();
            fragmentManager.beginTransaction().show(mVideoFragment);
            mVideoFragment.setOnCutRectChangeListener(new ICutRegionFragment.OnCutRectChangedListener() {
                @Override
                public void onScaleAndRotate(float scale, float degree) {
                    mCuttingMenuView.setProgress(degree);
                }

                @Override
                public void onSizeChanged(Point size) {
                    mPresenter.handleCutData(size);
                    mVideoFragment.setCutData(mPresenter.getCutData());
                }
            });
        } else {
            finish();
        }

    }

    @Override
    public ICutRegionFragment getCutUiView() {
        return mVideoFragment;
    }

    @Override
    public void exit() {
        setResult(RESULT_OK);
        AppManager.getInstance().finishActivity();
    }
}
