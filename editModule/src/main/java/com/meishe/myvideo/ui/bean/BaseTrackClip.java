package com.meishe.myvideo.ui.bean;


import androidx.annotation.NonNull;

import com.google.gson.Gson;


/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/3/5 11:24
 * @Description :轨道视图相关的基础基类 The underlying base class associated with the track view
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public abstract class BaseTrackClip implements ITrackClip {
    public static final String CLIP_IMAGE = "image";
    public static final String CLIP_HOLDER = "holder";
    public static final String CLIP_VIDEO = "video";
    public static final long MIN_DURATION = 100000;
    private String name;
    private String type;
    private SpeedInfo speedInfo;
    private KeyFrameInfo keyFrameInfo;
    private int trackIndex;
    private int indexInTrack;
    private long inPoint;
    private long outPoint;
    private long trimIn;
    private long trimOut;
    /**
     * 该片段的最初始时长(未处理分割、变速前的时长,也是真实文件的时长)
     * The initial duration of the segment (the duration before the split and speed change are not processed,
     * but also the duration of the real file)
     */
    private long originalDuration;

    /**
     * 是否有道具标记
     *Is there a prop mark
     */
    private boolean hasProp;

    /**
     * 音量
     * the volume
     */
    private float volume;


    BaseTrackClip() {
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public int getTrackIndex() {
        return trackIndex;
    }

    @Override
    public void setTrackIndex(int trackIndex) {
        this.trackIndex = trackIndex;
    }

    @Override
    public long getInPoint() {
        return inPoint;
    }

    @Override
    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    @Override
    public long getOutPoint() {
        return outPoint;
    }

    @Override
    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    @Override
    public long getTrimIn() {
        return trimIn;
    }

    @Override
    public void setTrimIn(long trimIn) {
        this.trimIn = trimIn;
    }

    @Override
    public int getIndexInTrack() {
        return indexInTrack;
    }

    @Override
    public void setIndexInTrack(int index) {
        indexInTrack = index;
    }

    @Override
    public long getTrimOut() {
        return trimOut;
    }

    @Override
    public void setTrimOut(long trimOut) {
        this.trimOut = trimOut;
    }

    @Override
    public long getOriginalDuration() {
        return originalDuration;
    }

    @Override
    public void setOriginalDuration(long originalDuration) {
        this.originalDuration = originalDuration;
    }

    @Override
    public SpeedInfo getSpeedInfo() {
        if (speedInfo == null) {
            speedInfo = new SpeedInfo();
        }
        return speedInfo;
    }

    @Override
    public void setSpeedInfo(SpeedInfo speedInfo) {
        this.speedInfo = speedInfo;
    }

    @Override
    public void setKeyFrameInfo(KeyFrameInfo keyFrameInfo) {
        this.keyFrameInfo = keyFrameInfo;
    }

    @Override
    public KeyFrameInfo getKeyFrameInfo() {
        if (keyFrameInfo == null) {
            keyFrameInfo = new KeyFrameInfo();
        }
        return keyFrameInfo;
    }

    @Override
    public void setAssetPath(String assetPath) {

    }

    @Override
    public String getAssetPath() {
        return null;
    }

    @Override
    public void setCoverPath(String coverPath) {

    }

    @Override
    public String getCoverPath() {
        return null;
    }

    @Override
    public boolean hasProp() {
        return hasProp;
    }

    @Override
    public void setHasProp(boolean hasProp) {
        this.hasProp = hasProp;
    }

    @Override
    public void setVolume(float volume) {
        this.volume = volume;
    }

    @Override
    public float getVolume() {
        return volume;
    }

    @Override
    public ITrackClip copy() {
        Gson gson = new Gson();
        return gson.fromJson(gson.toJson(this), getClass());
    }

    @Override
    public ThumbNailInfo getThumbNailInfo() {
        return null;
    }

    @Override
    public void setThumbNailInfo(ThumbNailInfo thumbNailInfo) {

    }

    @NonNull
    @Override
    public String toString() {
        return "BaseTrackClip{type=" + getType() + ",trackIndex=" + getTrackIndex() + ",indexInTrack=" + getIndexInTrack()
                + ",inPoint=" + getInPoint()
                + ",outPoint=" + getOutPoint() + ",trimIn=" + getTrimIn() + ",trimOut=" + getTrimOut()
                + ",originalDuration=" + getOriginalDuration() + "}";
    }
}
