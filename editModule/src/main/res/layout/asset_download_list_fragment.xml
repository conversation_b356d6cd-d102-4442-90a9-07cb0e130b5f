<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/menu_bg">

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none"
        android:visibility="visible">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_asset_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="none" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:id="@+id/ll_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_ffffffff"
        android:gravity="center"
        android:orientation="vertical">

        <ProgressBar
            android:layout_width="@dimen/dp_px_150"
            android:layout_height="@dimen/dp_px_150"
            android:indeterminateBehavior="repeat"
            android:indeterminateDrawable="@drawable/progress_eight_petal_xml" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_50"
            android:text="@string/asset_loading"
            android:textAlignment="center"
            android:textColor="@color/color_ff4d4f51"
            android:textSize="@dimen/sp_px_57" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_load_failed"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_ffffffff"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="@string/asset_loadfailed"
            android:textColor="@color/color_ffd0021b"
            android:textSize="@dimen/sp_px_57" />

        <Button
            android:id="@+id/bt_reload"
            android:layout_width="@dimen/dp_px_240"
            android:layout_height="@dimen/dp_px_80"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp_px_132"
            android:background="@color/color_ff4a90e2"
            android:text="@string/retry"
            android:textAllCaps="false"
            android:textColor="@color/color_ffffffff"
            android:textSize="@dimen/sp_px_36" />
    </LinearLayout>
</RelativeLayout>
