<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="266dp"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_corn_select_ratio"
    android:orientation="vertical">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:text="@string/prompt"
        android:textColor="@color/white_8"
        android:textSize="15sp"
        android:layout_gravity="center_horizontal"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#979797"
        android:layout_marginLeft="7dp"
        android:layout_marginRight="7dp"
        android:layout_marginTop="10dp"/>

    <TextView
        android:id="@+id/dialog_first_tip"
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:layout_gravity="center_horizontal"
        android:textAlignment="center"
        android:textColor="@color/white_8"
        android:textSize="17sp" />

    <TextView
        android:id="@+id/dialog_second_tip"
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:textAlignment="center"
        android:textColor="@color/white_8"
        android:textSize="12sp"
        android:visibility="gone"/>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#979797"
        android:layout_marginLeft="7dp"
        android:layout_marginRight="7dp"
        android:layout_marginTop="17dp"/>

    <LinearLayout
        android:id="@+id/btn_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@null"
        android:orientation="horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:visibility="visible">

        <RelativeLayout
            android:id="@+id/left_btn_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <Button
                android:id="@+id/left_btn"
                android:layout_width="50dp"
                android:layout_height="20dp"
                android:background="@null"
                android:textColor="#4A90E2"
                android:textSize="15sp"
                android:layout_centerInParent="true"/>
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/right_btn_layout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <Button
                android:id="@+id/right_btn"
                android:layout_width="50dp"
                android:layout_height="20dp"
                android:background="@null"
                android:textColor="#4A90E2"
                android:textSize="15sp"
                android:layout_centerInParent="true"/>
        </RelativeLayout>
    </LinearLayout>

</LinearLayout>