package com.meishe.myvideo.ui.trackview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import com.meishe.myvideo.R;
import com.meishe.myvideo.util.PixelPerMicrosecondUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 时间轴打点标记视图
 * Timeline mark view for displaying time markers
 */
public class TimelineMarkView extends View implements PixelPerMicrosecondUtil.PixelPerMicrosecondChangeListener {
    
    private static final String TAG = "TimelineMarkView";
    
    // 标记点数据
    private List<TimelineMark> mTimelineMarks = new ArrayList<>();
    
    // 绘制相关
    private Paint mMarkPaint;
    private RectF mMarkRect;
    
    // 标记点样式配置
    private int mMarkColor;
    private int mMarkWidth;
    private int mMarkHeight;
    private int mMarkCornerRadius;
    
    // 时间轴相关
    private long mTimelineDuration;
    private int mStartPadding;
    
    /**
     * 时间轴标记点数据类
     */
    public static class TimelineMark {
        public long timestamp;  // 时间戳（微秒）
        public String label;    // 标记标签（可选）
        
        public TimelineMark(long timestamp) {
            this.timestamp = timestamp;
        }
        
        public TimelineMark(long timestamp, String label) {
            this.timestamp = timestamp;
            this.label = label;
        }
    }
    
    public TimelineMarkView(Context context) {
        super(context);
        init(context);
    }
    
    public TimelineMarkView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }
    
    public TimelineMarkView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        // 注册像素变化监听
        PixelPerMicrosecondUtil.addPixelPerMicrosecondChangeListener(this);
        
        // 初始化绘制工具
        mMarkPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mMarkRect = new RectF();
        
        // 初始化样式配置
        mMarkColor = context.getResources().getColor(R.color.timeline_mark_color);
        mMarkWidth = context.getResources().getDimensionPixelSize(R.dimen.timeline_mark_width);
        mMarkHeight = context.getResources().getDimensionPixelSize(R.dimen.timeline_mark_height);
        mMarkCornerRadius = context.getResources().getDimensionPixelSize(R.dimen.timeline_mark_corner_radius);
        
        // 设置画笔
        mMarkPaint.setColor(mMarkColor);
        mMarkPaint.setStyle(Paint.Style.FILL);
        
        // 获取起始边距
        mStartPadding = context.getResources().getDisplayMetrics().widthPixels / 2;
    }
    
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (mTimelineMarks.isEmpty()) {
            return;
        }
        
        // 绘制所有标记点
        for (TimelineMark mark : mTimelineMarks) {
            drawMark(canvas, mark);
        }
    }
    
    /**
     * 绘制单个标记点
     */
    private void drawMark(Canvas canvas, TimelineMark mark) {
        // 计算标记点的X位置
        int markX = getMarkPositionX(mark.timestamp);
        
        // 检查标记点是否在可见区域内
        if (markX < 0 || markX > getWidth()) {
            return;
        }
        
        // 计算标记点的Y位置（垂直居中）
        int markY = (getHeight() - mMarkHeight) / 2;
        
        // 设置绘制区域
        mMarkRect.set(
            markX - mMarkWidth / 2f,
            markY,
            markX + mMarkWidth / 2f,
            markY + mMarkHeight
        );
        
        // 绘制圆角矩形标记点
        canvas.drawRoundRect(mMarkRect, mMarkCornerRadius, mMarkCornerRadius, mMarkPaint);
    }
    
    /**
     * 根据时间戳计算标记点的X位置
     */
    private int getMarkPositionX(long timestamp) {
        return mStartPadding + PixelPerMicrosecondUtil.durationToLength(timestamp);
    }
    
    /**
     * 添加时间轴标记点
     */
    public void addTimelineMark(long timestamp) {
        addTimelineMark(new TimelineMark(timestamp));
    }
    
    /**
     * 添加带标签的时间轴标记点
     */
    public void addTimelineMark(long timestamp, String label) {
        addTimelineMark(new TimelineMark(timestamp, label));
    }
    
    /**
     * 添加时间轴标记点
     */
    public void addTimelineMark(TimelineMark mark) {
        if (mark != null && mark.timestamp >= 0 && mark.timestamp <= mTimelineDuration) {
            mTimelineMarks.add(mark);
            invalidate(); // 触发重绘
        }
    }
    
    /**
     * 移除指定时间戳的标记点
     */
    public void removeTimelineMark(long timestamp) {
        mTimelineMarks.removeIf(mark -> mark.timestamp == timestamp);
        invalidate();
    }
    
    /**
     * 清除所有标记点
     */
    public void clearTimelineMarks() {
        mTimelineMarks.clear();
        invalidate();
    }
    
    /**
     * 获取所有标记点
     */
    public List<TimelineMark> getTimelineMarks() {
        return new ArrayList<>(mTimelineMarks);
    }
    
    /**
     * 设置时间轴总时长
     */
    public void setTimelineDuration(long duration) {
        mTimelineDuration = duration;
        // 移除超出时长的标记点
        mTimelineMarks.removeIf(mark -> mark.timestamp > duration);
        invalidate();
    }
    
    /**
     * 设置标记点颜色
     */
    public void setMarkColor(int color) {
        mMarkColor = color;
        mMarkPaint.setColor(color);
        invalidate();
    }
    
    /**
     * 设置标记点尺寸
     */
    public void setMarkSize(int width, int height) {
        mMarkWidth = width;
        mMarkHeight = height;
        invalidate();
    }
    
    @Override
    public void onPixelPerMicrosecondChange(double pixelPerMicrosecond, float scale) {
        // 当缩放比例改变时，重新绘制标记点
        invalidate();
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        // 移除监听器
        PixelPerMicrosecondUtil.removePixelPerMicrosecondChangeListener(this);
    }
}
