package com.meishe.myvideo.view.interf;

import com.meishe.business.assets.iview.AssetsView;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2021/8/5 19:26
 * @Description :滤镜View接口定义 Filter view interface definition.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public interface FilterView extends AssetsView {
    /**
     * Sets progress.
     * 设置进度
     *
     * @param progress the progress 进度
     */
    void setProgress(float progress);

    /**
     * Update select position.
     * 更新选择位置
     *
     * @param selection the selection 选择
     */
    void updateSelectPosition(int selection);

    /**
     * Need show apply all
     * 是否需要显式应用到全部
     *
     * @return true:yes;false：no
     */
    boolean needShowApplyAll();
}
