package com.meishe.myvideo.view.editview;

import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.meishe.myvideo.R;
import com.meishe.myvideo.interfaces.BottomEventListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 变速
 * change speed
 */
public class EditChangeSpeedView extends RelativeLayout {
    private ImageView mIvConfirm;
    private TextView mTvContent;
    private CheckBox mCheckBox;
    private ChangeSpeedListener mEventListener;
    private EditChangeSpeedScrollView speedScrollView;
    private float mSpeed;

    public EditChangeSpeedView(Context context) {
        this(context, null);
    }

    public EditChangeSpeedView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public EditChangeSpeedView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initData();
        initListener();
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public EditChangeSpeedView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        initView();
        initData();
        initListener();
    }

    /**
     * Init view.
     * 初始化视图
     */
    protected void initView() {
        View rootView = LayoutInflater.from(getContext()).inflate(R.layout.view_edit_chang_speed, this);
        mIvConfirm = rootView.findViewById(R.id.iv_confirm);
        mTvContent = rootView.findViewById(R.id.tv_content);
        mCheckBox = rootView.findViewById(R.id.ck_change_voice);
        speedScrollView = rootView.findViewById(R.id.speed_view);
    }

    /**
     * Init data.
     * 初始化数据
     */
    protected void initData() {
        mTvContent.setText(R.string.menu_sub_tab_change_speed_practice);
        postDelayed(new Runnable() {
            @Override
            public void run() {
                List<EditChangeSpeedScrollView.SpeedParam> speedParams = new ArrayList<>();
                EditChangeSpeedScrollView.SpeedParam param0 = new EditChangeSpeedScrollView.SpeedParam(0.2f);
                EditChangeSpeedScrollView.SpeedParam param1 = new EditChangeSpeedScrollView.SpeedParam(1.0f);
                EditChangeSpeedScrollView.SpeedParam param2 = new EditChangeSpeedScrollView.SpeedParam(2.0f);
                EditChangeSpeedScrollView.SpeedParam param3 = new EditChangeSpeedScrollView.SpeedParam(3.0f);
                EditChangeSpeedScrollView.SpeedParam param4 = new EditChangeSpeedScrollView.SpeedParam(4.0f);
                speedParams.add(param0);
                speedParams.add(param1);
                speedParams.add(param2);
                speedParams.add(param3);
                speedParams.add(param4);
//                if (Constants.NEED_100_SPEED) {
//                    EditChangeSpeedScrollView.SpeedParam param5 = new EditChangeSpeedScrollView.SpeedParam(100.0f);
//                    speedParams.add(param5);
//                }
                speedScrollView.setSelectedData(speedParams);
            }
        }, 100);
    }


    /**
     * Sets speed.
     * 设置速度
     * @param speed         the speed 速度
     * @param isChangeVoice the is change voice 换声点
     */
    public void setSpeed(float speed, boolean isChangeVoice) {
        mSpeed = speed;
        mCheckBox.setChecked(!isChangeVoice);
        speedScrollView.setCurrentSpeed(speed);
    }

    /**
     * Init listener.
     * 初始化监听
     */
    protected void initListener() {
        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
        /*
         * 变调
         * modified tone
         */
        mCheckBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (mEventListener != null) {
                    mEventListener.onSpeedChange(mSpeed, !mCheckBox.isChecked());
                }
            }
        });

        speedScrollView.setOnSpeedChangedListener(new EditChangeSpeedScrollView.OnSpeedChangedListener() {
            @Override
            public void onSpeedChanged(float speed) {
                mSpeed = speed;
                if (mEventListener != null) {
                    mEventListener.onSpeedChange(speed, !mCheckBox.isChecked());
                }
            }
        });
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener 监听
     */
    public void setListener(ChangeSpeedListener listener) {
        mEventListener = listener;
    }


    /**
     * The type Change speed listener.
     * 变速监听器类
     */
    public abstract static class ChangeSpeedListener extends BottomEventListener {
        /**
         * On speed change.
         * 变速
         * @param speed       the speed 速度
         * @param changeVoice the change voice 换声点
         */
        public abstract void onSpeedChange(float speed, boolean changeVoice);
    }
}
