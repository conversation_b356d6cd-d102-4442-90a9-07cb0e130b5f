package com.meishe.engine.command;

import android.graphics.PointF;

import com.meishe.annotation.Undo;
import com.meishe.base.utils.LogUtils;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.ClipInfo;
import com.meishe.engine.bean.MeicamCaptionClip;
import com.meishe.engine.bean.MeicamStickerCaptionTrack;
import com.meishe.engine.bean.MeicamTimeline;

import java.util.UUID;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/24 16:07
 * @Description :字幕命令 The caption command
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CaptionCommand {

    private final static String TAG = "Caption";
    public static final int PARAM_LETTER_SPACING = 1;
    public static final int PARAM_TEXT = 2;
    public static final int PARAM_CAPTION_STYLE = 3;
    public static final int PARAM_SCALE_X = 4;
    public static final int PARAM_SCALE_Y = 5;
    public static final int PARAM_ROTATION = 6;
    public static final int PARAM_TRANS_X = 7;
    public static final int PARAM_TRANS_Y = 8;
    public static final int PARAM_FONT = 9;
    public static final int PARAM_FONT_PATH = 10;
    public static final int PARAM_TEXT_COLOR = 11;
    public static final int PARAM_IS_BOLD = 12;
    public static final int PARAM_IS_ITALIC = 13;
    public static final int PARAM_IS_SHADOW = 14;
    public static final int PARAM_IS_OUTLINE = 15;
    public static final int PARAM_OUTLINE_COLOR = 16;
    public static final int PARAM_BACKGROUND_COLOR = 17;
    public static final int PARAM_OUTLINE_WIDTH = 18;
    public static final int PARAM_BACKGROUND_RADIUS = 19;
    public static final int PARAM_LINE_SPACING = 20;
    public static final int PARAM_TEXT_ALIGNMENT = 21;
    public static final int PARAM_RICH_WORD = 22;
    public static final int PARAM_BUBBLE = 23;
    public static final int PARAM_COMP_ANIMATION = 24;
    public static final int PARAM_COMP_ANIMATION_DURATION = 25;
    public static final int PARAM_IN_ANIMATION = 26;
    public static final int PARAM_IN_ANIMATION_DURATION = 27;
    public static final int PARAM_OUT_ANIMATION = 28;
    public static final int PARAM_OUT_ANIMATION_DURATION = 29;
    public static final int PARAM_OPERATION_TYPE = 30;

    @Undo(className = "CaptionScaleParamCommand", function = "scaleCaption",
            param = {"float|scaleFactor", "float|anchorX", "float|anchorY", "boolean...|needSaveOperate"})
    public static void scaleCaption(MeicamCaptionClip captionClip, float scaleFactor, float anchorX, float anchorY, boolean... needSaveOperate) {
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + UUID.randomUUID();
            float oldAnchorX = anchorX;
            float oldAnchorY = anchorY;
            PointF anchorForScale = captionClip.getAnchorForScale();
            if (anchorForScale != null) {
                oldAnchorX = anchorForScale.x;
                oldAnchorY = anchorForScale.y;
            }
            Object[] unDoParam = new Object[]{1F / scaleFactor, oldAnchorX, oldAnchorY, new boolean[]{false}};
            Object[] redoParam = new Object[]{scaleFactor, anchorX, anchorY};
            CommandUtil.saveOperate("CaptionScaleParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        captionClip.scaleCaption(scaleFactor, new PointF(anchorX, anchorY));
    }

    /**
     * Sets param.
     * 设置参数
     *
     * @param captionClip     the caption clip
     * @param paramType       the param type 参数类型
     * @param param           the param 参数
     * @param needSaveOperate the need save operate 是否保存操作
     */
    @Undo(className = "CaptionSetParamCommand", function = "setParam",
            param = {"int|paramType", "Object|param", "boolean...|needSaveOperate"})
    public static boolean setParam(MeicamCaptionClip captionClip, int paramType, Object param, boolean... needSaveOperate) {
        return setParamWidthOld(captionClip, paramType, null, param, needSaveOperate);
    }

    /**
     * Sets param.
     * 设置参数
     *
     * @param captionClip     the caption clip
     * @param paramType       the param type 参数类型
     * @param oldParam        the old param 旧参数
     * @param param           the param 参数
     * @param needSaveOperate the need save operate 是否保存操作
     */
    public static boolean setParamWidthOld(MeicamCaptionClip captionClip, int paramType, Object oldParam, Object param, boolean... needSaveOperate) {
        boolean result = true;
        Object lastParam = null;
        try {
            switch (paramType) {
                case PARAM_LETTER_SPACING:
                    lastParam = captionClip.getLetterSpacing();
                    captionClip.setLetterSpacing((Float) param);
                    break;
                case PARAM_TEXT:
                    lastParam = captionClip.getText();
                    captionClip.setText((String) param);
                    break;
                case PARAM_CAPTION_STYLE:
                    lastParam = captionClip.getStyleId();
                    captionClip.setStyleId((String) param);
                    break;
                case PARAM_SCALE_X:
                    lastParam = captionClip.getScaleX();
                    captionClip.setScaleX((Float) param);
                    break;
                case PARAM_SCALE_Y:
                    lastParam = captionClip.getScaleY();
                    captionClip.setScaleY((Float) param);
                    break;
                case PARAM_ROTATION:
                    lastParam = captionClip.getRotation();
                    captionClip.setRotation((Float) param);
                    break;
                case PARAM_TRANS_X:
                    lastParam = captionClip.getTranslationX();
                    captionClip.setTranslationX((Float) param);
                    break;
                case PARAM_TRANS_Y:
                    lastParam = captionClip.getTranslationY();
                    captionClip.setTranslationY((Float) param);
                    break;
                case PARAM_FONT:
                    lastParam = captionClip.getFont();
                    captionClip.setFont((String) param);
                    break;
                case PARAM_FONT_PATH:
                    lastParam = captionClip.getFontPath();
                    captionClip.setFontByFilePath((String) param);
                    break;
                case PARAM_TEXT_COLOR:
                    lastParam = captionClip.getTextColor();
                    captionClip.setTextColor((float[]) param);
                    break;
                case PARAM_IS_BOLD:
                    lastParam = captionClip.isBold();
                    captionClip.setBold((Boolean) param);
                    break;
                case PARAM_IS_ITALIC:
                    lastParam = captionClip.isItalic();
                    captionClip.setItalic((Boolean) param);
                    break;
                case PARAM_IS_SHADOW:
                    lastParam = captionClip.isShadow();
                    captionClip.setShadow((Boolean) param);
                    break;
                case PARAM_IS_OUTLINE:
                    lastParam = captionClip.isOutline();
                    captionClip.setOutline((Boolean) param);
                    break;
                case PARAM_OUTLINE_COLOR:
                    lastParam = captionClip.getOutlineColor();
                    captionClip.setOutlineColor((float[]) param);
                    break;
                case PARAM_BACKGROUND_COLOR:
                    lastParam = captionClip.getBackgroundColor();
                    captionClip.setBackgroundColor((float[]) param);
                    break;
                case PARAM_OUTLINE_WIDTH:
                    lastParam = captionClip.getOutlineWidth();
                    captionClip.setOutlineWidth((Float) param);
                    break;
                case PARAM_BACKGROUND_RADIUS:
                    lastParam = captionClip.getBackgroundRadius();
                    captionClip.setBackgroundRadius((Float) param);
                    break;
                case PARAM_LINE_SPACING:
                    lastParam = captionClip.getLineSpacing();
                    captionClip.setLineSpacing((Float) param);
                    break;
                case PARAM_TEXT_ALIGNMENT:
                    lastParam = captionClip.getTextAlignment();
                    captionClip.setTextAlignment((Integer) param);
                    break;
                case PARAM_RICH_WORD:
                    lastParam = captionClip.getRichWordUuid();
                    result = captionClip.setRichWordUuid((String) param);
                    break;
                case PARAM_BUBBLE:
                    lastParam = captionClip.getBubbleUuid();
                    result = captionClip.setBubbleUuid((String) param);
                    break;
                case PARAM_COMP_ANIMATION:
                    lastParam = captionClip.getCombinationAnimationUuid();
                    result = captionClip.setCombinationAnimationUuid((String) param);
                    break;
                case PARAM_COMP_ANIMATION_DURATION:
                    lastParam = captionClip.getCombinationAnimationDuration();
                    captionClip.setCombinationAnimationDuration((Integer) param);
                    break;
                case PARAM_IN_ANIMATION:
                    lastParam = captionClip.getMarchInAnimationUuid();
                    result = captionClip.setMarchInAnimationUuid((String) param);
                    break;
                case PARAM_IN_ANIMATION_DURATION:
                    lastParam = captionClip.getMarchInAnimationDuration();
                    captionClip.setMarchInAnimationDuration((Integer) param);
                    break;
                case PARAM_OUT_ANIMATION:
                    lastParam = captionClip.getMarchOutAnimationUuid();
                    result = captionClip.setMarchOutAnimationUuid((String) param);
                    break;
                case PARAM_OUT_ANIMATION_DURATION:
                    lastParam = captionClip.getMarchOutAnimationDuration();
                    captionClip.setMarchOutAnimationDuration((Integer) param);
                    break;
                case PARAM_OPERATION_TYPE:
                    lastParam = captionClip.getOperationType();
                    captionClip.setOperationType((Integer) param);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            LogUtils.e(e);
        }
        if (CommandUtil.needSaveOperate(needSaveOperate)) {
            String tag = getTag(captionClip);
            String tagAndKey = tag + paramType;
            Object[] unDoParam;
            // 优先使用传入的oldParam
            if (oldParam == null) {
                unDoParam = new Object[]{paramType, lastParam, new boolean[]{false}};
            } else {
                unDoParam = new Object[]{paramType, oldParam, new boolean[]{false}};
            }
            LogUtils.d("needSaveOperate,oldParam="+oldParam+",lastParam="+lastParam);
            Object[] redoParam = new Object[]{paramType, param};
            CommandUtil.saveOperate("CaptionSetParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        return result;
    }

    public static String getTag(MeicamCaptionClip captionClip) {
        // 因为captionClip的inPoint（单独使用index会有批量删除ai字幕和普通字幕窜轨并部分转换成普通字幕的问题）会发生变化，
        // 所以单独使用inPoint作为tag可能无法找到相应的captionClip，增加创建时标记，在通过inPoint找不到的时候进行二次查找。
        return TAG + captionClip.getTrackIndex() + "|" + captionClip.getInPoint() +"|" + captionClip.getCreateTag();
    }


    public static MeicamCaptionClip getItByTag(String tag) {
        String[] split = tag.replaceAll(TAG, "").split("\\|");
        int trackIndex = Integer.parseInt(split[0]);
        long inPoint = Long.parseLong(split[1]);
        String createTag = split[2];
        MeicamTimeline currentTimeline = EditorEngine.getInstance().getCurrentTimeline();
        MeicamStickerCaptionTrack stickCaptionTrack = currentTimeline.findStickCaptionTrack(trackIndex);
        if (stickCaptionTrack != null) {
            ClipInfo<?> captionStickerClip = stickCaptionTrack.getCaptionStickerClip(inPoint);
            if(captionStickerClip != null){
                return (MeicamCaptionClip) captionStickerClip;
            }
            // 因为captionClip的inPoint（index也可能会变）会发生变化，所以单独使用inPoint作为tag可能无法找到相应的captionClip，
            // 增加创建时标记，在通过inPoint找不到的时候通过该标记进行二次查找。
            LogUtils.d("tag="+tag+",not find clip by inPoint="+inPoint);
            int clipCount = stickCaptionTrack.getClipCount();
            for (int i = 0; i < clipCount; i++) {
                ClipInfo<?> clip = stickCaptionTrack.getCaptionStickerClip(i);
                if(clip.sameCreateTag(createTag)){
                    return (MeicamCaptionClip) clip;
                }
            }
            LogUtils.e("tag="+tag+",not find clip by createTag");
        }
        return null;
    }

    @Undo(className = "CaptionTranslateCommand", function = "translateCaption",
            param = {"float|transX", "float|transY", "boolean...|needSaveOperate"})
    public static void translateCaption(MeicamCaptionClip caption, float transX, float transY, boolean... needSavaOperate) {
        caption.translateCaption(new PointF(transX, transY));
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(caption);

            String tagAndKey = tag + System.nanoTime();
            Object[] unDoParam = new Object[]{-transX, -transY, new boolean[]{false}};
            Object[] redoParam = new Object[]{transX, transY};
            CommandUtil.saveOperate("CaptionTranslateCommand", unDoParam, redoParam, tag, tagAndKey);
        }
    }

    @Undo(className = "CaptionRotateParamCommand", function = "rotateCaption",
            param = {"float|scaleFactor", "boolean...|needSaveOperate"})
    public static void rotateCaption(MeicamCaptionClip caption, float angle, boolean... needSavaOperate) {
        if (CommandUtil.needSaveOperate(needSavaOperate)) {
            String tag = getTag(caption);

            String tagAndKey = tag + UUID.randomUUID();
            Object[] unDoParam = new Object[]{-angle, new boolean[]{false}};
            Object[] redoParam = new Object[]{angle};
            CommandUtil.saveOperate("CaptionRotateParamCommand", unDoParam, redoParam, tag, tagAndKey);
        }
        caption.rotateCaption(angle);
    }
}
