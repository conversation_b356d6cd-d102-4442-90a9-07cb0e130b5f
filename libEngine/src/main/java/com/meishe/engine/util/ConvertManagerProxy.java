package com.meishe.engine.util;

import com.meishe.engine.observer.ConvertFileObserver;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/18 17:25
 * @Description :转码代理 The proxy of convert manager
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class ConvertManagerProxy implements IConvertManager {
   private IConvertManager executor;


   /**
    * Sets executor.
    * 设置执行者
    * @param executor the executor
    */
   public void setExecutor(IConvertManager executor) {
      this.executor = executor;
   }

   @Override
   public void convertFile(ConvertParam convertParam) {
      if (executor != null) {
         executor.convertFile(convertParam);
      }
   }

   @Override
   public void cancelConvert() {
      if (executor != null) {
         executor.cancelConvert();
      }
   }

   @Override
   public void registerConvertFileObserver(ConvertFileObserver observer) {
      if (executor != null) {
         executor.registerConvertFileObserver(observer);
      }
   }

   @Override
   public void unregisterConvertFileObserver(ConvertFileObserver observer) {
      if (executor != null) {
         executor.unregisterConvertFileObserver(observer);
      }
   }
}
