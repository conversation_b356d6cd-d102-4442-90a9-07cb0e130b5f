package com.meishe.engine.local;


import androidx.annotation.NonNull;

import com.google.gson.annotations.SerializedName;
import com.meishe.engine.bean.CommonData;
import com.meishe.engine.local.background.LMeicamStoryboardInfo;
import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 草稿视频/图片片段 The video clip of draft data
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamVideoClip extends LClipInfo implements Cloneable, Serializable {
    /**
     * 对应资源id 和resources对应
     */
    private String id;
    private String filePath;
    // 倒放路径
    private String reverseFilePath;
    // 区分图片还是视频
    private String videoType;
    private long trimIn;
    private long trimOut;
    private long orgDuration;
    private float volume = 1.0f;
    private double speed = 1.0f;
    private String curveSpeed = "";
    private String curveSpeedName = "";
    //是否倒放
    private boolean isVideoReverse = false;
    //视频默认未转码成功
    private boolean isConvertSuccess = false;

    /*
     * 图片展示模式
     * Picture display mode
     * */
    @SerializedName("imageDisplayMode")
    private int mImageDisplayMode = 0;

    private boolean imageMotionAnimationEnabled;

    /*
     * 视频横向裁剪，纵向平移
     * Video cropped horizontally, panned vertically
     * */
    @SerializedName("span")
    private float mSpan = 0;
    @SerializedName("scan")
    private float mScan = 0;


    //透明度
    private float opacity = 1f;

    private int extraRotation;
    //镜像
    private boolean reverse;
    //clip设置的VideoFx 如滤镜 transform 2D 等
    private List<LMeicamVideoFx> videoFxs = new ArrayList<>();

    @SerializedName("adjustData")
    private LMeicamAdjustData mAdjustData = new LMeicamAdjustData();
    /**
     * 在主题中的成分。片头或者片尾
     */
    @SerializedName("roleInTheme")
    private int mRoleInTheme;

    //美肤和美型
    @SerializedName("faceEffectParameter")
    private Map<String, Float> mFaceEffectParameter = new HashMap<>();

    @SerializedName("storyboardInfo")
    private List<LMeicamStoryboardInfo> mStoryboardInfos = new ArrayList<>();
    /**
     * 变调
     */
    @SerializedName("keepAudioPitch")
    private boolean keepAudioPitch = true;

    @SerializedName("originalWidth")
    private int originalWidth;
    @SerializedName("originalHeight")
    private int originalHeight;

    private int blendingMode;

    /**
     * mask Data
     * 蒙版数据
     */
    public LNvMaskModel maskModel = new LNvMaskModel();

    /**
     * 音频特效集合
     * The audio fx list
     */
    private List<LMeicamAudioFx> audioFxList = new ArrayList<>();

    public LMeicamVideoClip() {
        super(CommonData.CLIP_VIDEO);
    }

    private String resourceId;

    /**
     * 远程路径
     * The remote path of video clip
     */
    private String remotePath;

    /**
     * 左声道波形
     * The url for left channel
     */
    private String leftChannelUrl;

    /**
     * 右声道波形
     * The url for right channel
     */
    private String rightChannelUrl;

    private ThumbNailInfo thumbNailInfo;

    /**
     * 音频淡入的时长
     */
    private long fadeInDuration;
    /**
     * 音频淡出的时长
     */
    private long fadeOutDuration;

    private boolean enableRawSourceMode;

    private float rectWidth;

    private float rectHeight;

    public LMeicamVideoClip(String filePath, String videoType, long orgDuration) {
        super(CommonData.CLIP_VIDEO);
        this.filePath = filePath;
        this.videoType = videoType;
        this.orgDuration = orgDuration;
    }

    public Map<String, Float> getFaceEffectParameter() {
        return mFaceEffectParameter;
    }

    public void setFaceEffectParameter(Map<String, Float> faceEffectParameter) {
        mFaceEffectParameter = faceEffectParameter;
    }

    public void setStoryboardInfos(List<LMeicamStoryboardInfo> storyboardInfos) {
        mStoryboardInfos = storyboardInfos;
    }

    public List<LMeicamStoryboardInfo> getStoryboardInfos() {
        return mStoryboardInfos;
    }

    public LMeicamAdjustData getMeicamAdjustData() {
        return mAdjustData;
    }

    public void setMeicamAdjustData(LMeicamAdjustData meicamAdjustData) {
        mAdjustData = meicamAdjustData;
    }

    public int getRoleInTheme() {
        return mRoleInTheme;
    }

    public void setRoleInTheme(int roleInTheme) {
        mRoleInTheme = roleInTheme;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getReverseFilePath() {
        return reverseFilePath;
    }

    public void setReverseFilePath(String reverseFilePath) {
        this.reverseFilePath = reverseFilePath;
    }

    public String getVideoType() {
        return videoType;
    }

    public void setVideoType(String videoType) {
        this.videoType = videoType;
    }

    public long getTrimIn() {
        return trimIn;
    }

    public void setTrimIn(long trimIn) {
        this.trimIn = trimIn;
    }

    public long getTrimOut() {
        return trimOut;
    }

    public void setTrimOut(long trimOut) {
        this.trimOut = trimOut;
    }

    public long getOrgDuration() {
        return orgDuration;
    }

    public void setOrgDuration(long orgDuration) {
        this.orgDuration = orgDuration;
    }

    public float getVolume() {
        return volume;
    }

    public void setVolume(float volume) {
        if (Float.isNaN(volume)) {
            return;
        }
        this.volume = volume;
    }

    public double getSpeed() {
        return speed;
    }

    public void setSpeed(double speed) {
        if (Double.isNaN(speed)) {
            return;
        }
        this.speed = speed;
    }

    public int getExtraRotation() {
        return extraRotation;
    }

    public void setExtraRotation(int extraRotation) {
        this.extraRotation = extraRotation;
    }

    public boolean isReverse() {
        return reverse;
    }

    public void setReverse(boolean reverse) {
        this.reverse = reverse;
    }

    public List<LMeicamVideoFx> getVideoFxs() {
        return videoFxs;
    }

    public float getOpacity() {
        return opacity;
    }

    public void setOpacity(float opacity) {
        if (Float.isNaN(opacity)) {
            return;
        }
        this.opacity = opacity;
    }

    public boolean getVideoReverse() {
        return isVideoReverse;
    }

    public void setVideoReverse(boolean videoReverse) {
        isVideoReverse = videoReverse;
    }

    public boolean isConvertSuccess() {
        return isConvertSuccess;
    }

    public void setConvertSuccess(boolean convertSuccess) {
        isConvertSuccess = convertSuccess;
    }


    public int getImageDisplayMode() {
        return mImageDisplayMode;
    }

    public void setmImageDisplayMode(int imageDisplayMode) {
        this.mImageDisplayMode = imageDisplayMode;
    }

    public String getCurveSpeed() {
        return curveSpeed;
    }

    public void setCurveSpeed(String curveSpeed) {
        this.curveSpeed = curveSpeed;
    }

    public String getCurveSpeedName() {
        return curveSpeedName;
    }

    public void setCurveSpeedName(String curveSpeedName) {
        this.curveSpeedName = curveSpeedName;
    }

    public boolean isKeepAudioPitch() {
        return keepAudioPitch;
    }

    public void setKeepAudioPitch(boolean keepAudioPitch) {
        this.keepAudioPitch = keepAudioPitch;
    }

    public void setScan(float scan) {
        if (Float.isNaN(scan)) {
            return;
        }
        this.mScan = scan;
    }

    public void setSpan(float span) {
        if (Float.isNaN(span)) {
            return;
        }
        this.mSpan = span;
    }

    public float getScan() {
        return mScan;
    }

    public float getSpan() {
        return mSpan;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public int getOriginalWidth() {
        return originalWidth;
    }

    public void setOriginalWidth(int originalWidth) {
        this.originalWidth = originalWidth;
    }

    public int getOriginalHeight() {
        return originalHeight;
    }

    public void setOriginalHeight(int originalHeight) {
        this.originalHeight = originalHeight;
    }

    public int getBlendingMode() {
        return blendingMode;
    }

    public void setBlendingMode(int blendingMode) {
        this.blendingMode = blendingMode;
    }

    public boolean isImageMotionAnimationEnabled() {
        return imageMotionAnimationEnabled;
    }

    public void setImageMotionAnimationEnabled(boolean imageMotionAnimationEnabled) {
        this.imageMotionAnimationEnabled = imageMotionAnimationEnabled;
    }

    public List<LMeicamAudioFx> getAudioFxList() {
        return audioFxList;
    }

    public void setAudioFxList(List<LMeicamAudioFx> audioFxList) {
        this.audioFxList = audioFxList;
    }

    public String getRemotePath() {
        return remotePath;
    }

    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath;
    }

    public String getLeftChannelUrl() {
        return leftChannelUrl;
    }

    public void setLeftChannelUrl(String leftChannelUrl) {
        this.leftChannelUrl = leftChannelUrl;
    }

    public String getRightChannelUrl() {
        return rightChannelUrl;
    }

    public void setRightChannelUrl(String rightChannelUrl) {
        this.rightChannelUrl = rightChannelUrl;
    }

    public long getFadeInDuration() {
        return fadeInDuration;
    }

    public void setFadeInDuration(long fadeInDuration) {
        this.fadeInDuration = fadeInDuration;
    }

    public long getFadeOutDuration() {
        return fadeOutDuration;
    }

    public void setFadeOutDuration(long fadeOutDuration) {
        this.fadeOutDuration = fadeOutDuration;
    }

    public ThumbNailInfo getThumbNailInfo() {
        return thumbNailInfo;
    }

    public void setThumbNailInfo(ThumbNailInfo thumbNailInfo) {
        this.thumbNailInfo = thumbNailInfo;
    }

    public boolean isEnableRawSourceMode() {
        return enableRawSourceMode;
    }

    public void setEnableRawSourceMode(boolean enableRawSourceMode) {
        this.enableRawSourceMode = enableRawSourceMode;
    }


    public float getRectWidth() {
        return rectWidth;
    }

    public void setRectWidth(float rectWidth) {
        this.rectWidth = rectWidth;
    }

    public float getRectHeight() {
        return rectHeight;
    }

    public void setRectHeight(float rectHeight) {
        this.rectHeight = rectHeight;
    }

    public static class ThumbNailInfo{
        public String urlPrefix;
        public long interval;
        public String extension;

        public ThumbNailInfo(String urlPrefix, long interval, String extension) {
            this.urlPrefix = urlPrefix;
            this.interval = interval;
            this.extension = extension;
        }
    }

    @NonNull
    @Override
    public Object clone() {
        return DeepCopyUtil.deepClone(this);
    }

}
