package com.meishe.myvideo.view.presenter;

import android.content.res.AssetManager;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.Utils;
import com.meishe.business.assets.presenter.AssetsPresenter;
import com.meishe.engine.asset.bean.AssetInfo;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.CanvasStyleInfo;
import com.meishe.myvideo.view.interf.BackgroundView;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class BackGroundPresenter extends AssetsPresenter<BackgroundView> {
    private final static String IMAGE_ASSETS_PATH = "background/image";

    public BackGroundPresenter() {
        super();
    }

    @Override
    protected List<AssetInfo> handleDataInFirstPage(List<AssetInfo> list) {
        List<AssetInfo> newData = new ArrayList<>();
        AssetInfo baseInfo = new CanvasStyleInfo();
        baseInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_add_resource));
        newData.add(baseInfo);
        baseInfo = new AssetInfo();
        baseInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_style_no));
        newData.add(baseInfo);
        newData.addAll(list);
        return newData;
    }

    public void loadData(final boolean needForceUpdate) {

        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null && userPlugin.isLogin()) {
            setPageSize(10);
            loadData(22, 0, 1, -1, needForceUpdate);
        } else {
            List<AssetInfo> canvasStyleList = getCanvasStyleList();
            if (CommonUtils.isEmpty(canvasStyleList)) {
                getView().onDataError(0, needForceUpdate);
            } else {
                getView().onNewDataBack(canvasStyleList, 0, needForceUpdate);
            }
        }

    }

    public boolean loadMoreData(final boolean needForceUpdate) {
        IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
        if (userPlugin != null && userPlugin.isLogin()) {
            return loadMoreData(22, 0, 1, -1, needForceUpdate);
        } else {
            return false;
        }
    }

    /**
     * 获取画布样式列表
     * Gets the canvas style list
     */
    public List<AssetInfo> getCanvasStyleList() {
        AssetManager assets = Utils.getApp().getAssets();
        try {
            String[] list = assets.list(IMAGE_ASSETS_PATH);
            if ((list == null) || (list.length <= 0)) {
                return null;
            }
            List<AssetInfo> result = new ArrayList<>();
            AssetInfo baseInfo = new CanvasStyleInfo();
            baseInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_add_resource));
            result.add(baseInfo);
            baseInfo = new AssetInfo();
            baseInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_canvas_style_no));
            result.add(baseInfo);
            for (String s : list) {
                baseInfo = new CanvasStyleInfo();
                baseInfo.setCoverPath("file:///android_asset/background/image/" + s);
                baseInfo.setAssetPath(s);
                baseInfo.setPackageId(s.split("\\.")[0]);
                result.add(baseInfo);
            }
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
