package com.meishe.myvideo.fragment.presenter;

import android.text.TextUtils;

import com.meishe.base.model.Presenter;
import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.DraftManager;
import com.meishe.draft.data.DraftData;
import com.meishe.libplugin.PluginManager;
import com.meishe.libplugin.user.IUserPlugin;
import com.meishe.myvideo.fragment.iview.DraftView;
import com.meishe.net.custom.BaseResponse;
import com.meishe.net.custom.RequestCallback;

import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2020/12/3 10:57
 * @Description :草稿业务处理类  The draft presenter
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DraftPresenter extends Presenter<DraftView> {

    /**
     * 获取剪辑草稿列表
     * Gets the clip draft list
     */
    public void getDraftList() {
        ThreadUtils.getIoPool().execute(new Runnable() {
            @Override
            public void run() {
                final List<DraftData> draftData = DraftManager.getInstance().getAllDraftData();
                IUserPlugin userPlugin = PluginManager.get().getUserPlugin();
                ThreadUtils.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (getView() != null) {
                            getView().onEditingDataBack(draftData);
                        }
                    }
                });
                if (userPlugin == null || !userPlugin.isLogin() || CommonUtils.isEmpty(draftData)) {
                    return;
                }
                String token = userPlugin.getToken();
                if (TextUtils.isEmpty(token)) {
                    return;
                }
                List<String> projectList = new ArrayList<>();
                List<DraftData> remoteDraft = new ArrayList<>();
                for (DraftData draftDatum : draftData) {
                    DraftData.CloudInfo cloudInfo = draftDatum.getCloudInfo();
                    if (cloudInfo != null && !TextUtils.isEmpty(cloudInfo.projectId)) {
                        projectList.add(cloudInfo.projectId);
                        remoteDraft.add(draftDatum);
                    }
                }
                if (projectList.isEmpty()) {
                   return;
                }
                userPlugin.getProjectBatchInfo(projectList, new RequestCallback<List<String>>() {
                    @Override
                    public void onSuccess(BaseResponse<List<String>> response) {
                        if (response != null && response.getData() != null) {
                            List<String> data = response.getData();
                            if (!CommonUtils.isEmpty(data)) {
                                for (DraftData draftDatum : remoteDraft) {
                                    draftDatum.setIsCloud(false);
                                }
                                for (String id : data) {
                                    for (DraftData draftDatum : remoteDraft) {
                                        DraftData.CloudInfo cloudInfo = draftDatum.getCloudInfo();
                                        if (cloudInfo != null
                                                && TextUtils.equals(cloudInfo.projectId, id)) {
                                            draftDatum.setIsCloud(true);
                                        }
                                    }
                                }
                            }
                        }
                        if (getView() != null) {
                            getView().onEditingDataBack(draftData);
                        }
                    }

                    @Override
                    public void onError(BaseResponse<List<String>> response) {
                        if (getView() != null) {
                            getView().onEditingDataBack(draftData);
                        }
                    }
                });
            }
        });
    }


    /**
     * 复制草稿
     * Copy draft
     *
     * @param draftData The draft data
     */
    public void copyDraft(DraftData draftData) {
        DraftManager.getInstance().copyDraft(draftData, null);
    }

    /**
     * 重命名草稿
     * Rename draft
     *
     * @param draftData The draft data
     * @param newName   The new name
     */
    public void renameDraft(DraftData draftData, String newName) {

        DraftManager.getInstance().renameDraft(draftData, newName, System.currentTimeMillis());

    }

    /**
     * 删除草稿
     * delete draft
     *
     * @param draftData The draft data
     */
    public void deleteDraft(DraftData draftData) {
        if (draftData == null) {
            return;
        }
        DraftManager.getInstance().deleteDraft(draftData);
    }

}
