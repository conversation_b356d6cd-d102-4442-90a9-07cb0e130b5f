package com.meishe.engine.observer;

import android.database.Observable;
import android.graphics.Bitmap;

import com.meicam.sdk.NvsTimeline;
import com.meishe.base.utils.ThreadUtils;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate :2021/5/18 15:11
 * @Description :编辑Engine的回调被观察者 The observable of engine callback
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class EngineCallbackObservable extends Observable<EngineCallbackObserver> {
    private String mCallbackFromTag;

    /**
     * 设置当前回调来自页面的标记
     * Sets the tag for the callback from the page
     *
     * @param callbackFromTag the callback from tag 标记
     */
    public void setCallbackFromTag(String callbackFromTag) {
        this.mCallbackFromTag = callbackFromTag;
    }

    /**
     * 获取当前回调来自页面的标记
     * Sets the tag for the callback from the page
     *
     * @return the callback from tag 标记
     */
    public String getCallbackFromTag() {
        return mCallbackFromTag;
    }

    /**
     * 导出中
     * On compile progress
     *
     * @param nvsTimeline the nvsTimeline 时间线
     * @param progress    the progress 进度
     */
    public void onCompileProgress(final NvsTimeline nvsTimeline, final int progress) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onCompileProgress(nvsTimeline, progress);
                    }
                }
            }
        });
    }

    /**
     * 导出完成
     * On compile complete
     *
     * @param nvsTimeline the nvsTimeline 时间线
     * @param isCanceled  true cancel 取消，false 成功
     */
    public void onCompileCompleted(final NvsTimeline nvsTimeline, final boolean isCanceled) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onCompileCompleted(nvsTimeline, isCanceled);
                    }
                }
            }
        });
    }

    /**
     * 导出失败
     * On compile failed
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onCompileFailed(final NvsTimeline nvsTimeline) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onCompileFailed(nvsTimeline);
                    }
                }
            }
        });
    }

    /**
     * 导出失败
     * On compile failed
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onCompileFinished(final NvsTimeline nvsTimeline) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onCompileFinished(nvsTimeline);
                    }
                }
            }
        });
    }

    /**
     * 抓图成功返回
     * on image grabbed  arrived
     *
     * @param bitmap the bitmap图片数据
     * @param time   the timestamp 获取到的图像时间戳
     */
    public void onImageGrabbedArrived(final Bitmap bitmap, final long time) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int index = mObservers.size() - 1; index >= 0; index--) {
                    EngineCallbackObserver observer = mObservers.get(index);
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onImageGrabbedArrived(bitmap, time);
                    }
                }
            }
        });
    }

    /**
     * 时间线播放预先加载完成
     * on playback preload completion
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onPlaybackPreloadingCompletion(final NvsTimeline nvsTimeline) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onPlaybackPreloadingCompletion(nvsTimeline);
                    }
                }
            }
        });
    }

    /**
     * 时间线播放停止
     * On playback stopped
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onPlaybackStopped(final NvsTimeline nvsTimeline) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onPlaybackStopped(nvsTimeline);
                    }
                }
            }
        });
    }

    /**
     * 时间线播放到结尾
     * On playback end
     *
     * @param nvsTimeline the nvsTimeline 时间线
     */
    public void onPlaybackEOF(final NvsTimeline nvsTimeline) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onPlaybackEOF(nvsTimeline);
                    }
                }
            }
        });
    }

    /**
     * 时间线播放的当前位置
     * On current playback  timestamp
     *
     * @param nvsTimeline the nvsTimeline 时间线
     * @param timestamp   the playing timestamp 当前的时间戳
     */
    public void onPlaybackTimelinePosition(final NvsTimeline nvsTimeline, final long timestamp) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onPlaybackTimelinePosition(nvsTimeline, timestamp);
                    }
                }
            }
        });
    }

    /**
     * 流媒体引擎状态改变
     * On streaming engine state changed
     */
    public void onStreamingEngineStateChanged(final int state) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onStreamingEngineStateChanged(state);
                    }
                }
            }
        });
    }

    /**
     * 当播放的首帧到来的时候
     * On first video frame presented
     */
    public void onFirstVideoFramePresented(final NvsTimeline nvsTimeline) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onFirstVideoFramePresented(nvsTimeline);
                    }
                }
            }
        });
    }

    public void onSeekingTimelinePosition(final NvsTimeline nvsTimeline, final long timeStamp) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onSeekingTimelinePosition(nvsTimeline, timeStamp);
                    }
                }
            }
        });
    }

    public void onFinishAssetPackageInstallation(final String s, final String s1, final int i, final int i1) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int index = mObservers.size() - 1; index >= 0; index--) {
                    EngineCallbackObserver observer = mObservers.get(index);
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onFinishAssetPackageInstallation(s, s1, i, i1);
                    }
                }
               /* for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onFinishAssetPackageInstallation(s, s1, i, i1);
                    }
                }*/
            }
        });
    }

    public void onFinishAssetPackageUpgrading(final String s, final String s1, final int i, final int i1) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int index = mObservers.size() - 1; index >= 0; index--) {
                    EngineCallbackObserver observer = mObservers.get(index);
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onFinishAssetPackageUpgrading(s, s1, i, i1);
                    }
                }
               /* for (EngineCallbackObserver observer : mObservers) {
                    observer.setCallbackFromTag(mCallbackFromTag);
                    if (observer.isActive()) {
                        observer.onFinishAssetPackageUpgrading(s, s1, i, i1);
                    }
                }*/
            }
        });
    }
}
