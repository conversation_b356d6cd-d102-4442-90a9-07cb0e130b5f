<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/dp_px_800"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_corn_select_ratio"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_30"
        android:text="@string/prompt"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_45" />

    <View
        android:id="@+id/v_title_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_3"
        android:layout_marginLeft="@dimen/dp_px_20"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginRight="@dimen/dp_px_20"
        android:background="@color/color_ff979797" />

    <TextView
        android:id="@+id/tv_first_tip"
        android:layout_width="@dimen/dp_px_720"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_54"
        android:text="@string/stayTuned"
        android:textAlignment="center"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_51" />

    <TextView
        android:id="@+id/tv_second_tip"
        android:layout_width="@dimen/dp_px_720"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp_px_15"
        android:text="@string/contactBusiness"
        android:textAlignment="center"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_36"
        android:visibility="gone" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_3"
        android:layout_marginLeft="@dimen/dp_px_20"
        android:layout_marginTop="@dimen/dp_px_50"
        android:layout_marginRight="@dimen/dp_px_20"
        android:background="@color/color_ff979797" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_30"
        android:layout_marginBottom="@dimen/dp_px_30"
        android:background="@null"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/tv_confirm_left"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_px_60"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/activity_cut_export_template_cancel"
            android:textColor="@color/color_ff4a90e2"
            android:textSize="@dimen/sp_px_45" />


        <TextView
            android:id="@+id/tv_confirm_right"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_px_60"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/i_know"
            android:textColor="@color/color_ff4a90e2"
            android:textSize="@dimen/sp_px_45" />
    </LinearLayout>

</LinearLayout>