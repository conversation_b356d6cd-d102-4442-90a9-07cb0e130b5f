<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/dp_px_255"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:contentDescription="@null" />

    <TextView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/tv_selected"
        android:layout_width="wrap_content"
        android:minWidth="@dimen/dp_px_105"
        android:layout_height="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_12"
        android:layout_marginStart="@dimen/dp_px_12"
        android:background="@drawable/bg_rectangle_round_white"
        android:gravity="center"
        android:text="@string/imported"
        android:textColor="@color/black"
        android:textSize="@dimen/sp_px_27"
        android:visibility="invisible" />

    <TextView
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp_px_12"
        android:layout_marginBottom="@dimen/dp_px_12"
        android:drawablePadding="@dimen/dp_px_10"
        android:gravity="center_vertical"
        android:textColor="@color/white"
        android:textSize="@dimen/sp_px_30" />

    <ImageView
        android:id="@+id/iv_duration"
        app:layout_constraintRight_toLeftOf="@+id/tv_duration"
        app:layout_constraintTop_toTopOf="@+id/tv_duration"
        app:layout_constraintBottom_toBottomOf="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@mipmap/material_video"/>

</androidx.constraintlayout.widget.ConstraintLayout>
