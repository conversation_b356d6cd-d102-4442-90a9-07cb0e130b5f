package com.meishe.engine.command;


import com.meishe.base.utils.LogUtils;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/8/31 14:32
 * @Description :命令工具 The command util
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class CommandUtil {

    /**
     * Need save operate boolean.
     * 是否需要保存操作
     *
     * @param needSaveOperate the need save operate
     * @return the boolean
     */
    public static boolean needSaveOperate(boolean... needSaveOperate) {
        try {
            return needSaveOperate.length == 0 || needSaveOperate[0];
        } catch (Exception e) {
            LogUtils.e(e);
        }
        return false;
    }

    /**
     * Save operate.
     * 保存操作
     * @param <T>       the type parameter 形参
     * @param className the class name 类名
     * @param undoArgs  the undo args 撤销保存的参数
     * @param redoArgs  the redo args 重做保存的参数
     * @param tag       the tag
     * @param tagAndKey the tag and key 加盐后的tag
     */
    public static <T extends Command> void saveOperate(String className, Object[] undoArgs, Object[] redoArgs, String tag, String tagAndKey) {
        try {
            long start = System.nanoTime();
            Class<T> aClass = (Class<T>) Class.forName("com.meishe.engine.command." + className);
            T command = CommandManager.getInstance().getCommand(tagAndKey, aClass);
            String canonicalName = aClass.getCanonicalName();
            if (command == null) {
                Class<?> innerUndoClass = Class.forName(canonicalName + "$UndoParam");
                Constructor<T> commandConstructor = aClass.getConstructor(String.class, innerUndoClass);
                Constructor<?> undoConstructor = innerUndoClass.getConstructors()[0];
                command = commandConstructor.newInstance(tag, undoConstructor.newInstance(undoArgs));
                CommandManager.getInstance().putCommand(tagAndKey, command);
            }
            Class<?> innerRedoClass = Class.forName(canonicalName + "$RedoParam");
            Method getRedoParam = aClass.getMethod("getRedoParam");
            Method setRedoParam = aClass.getMethod("setRedoParam", innerRedoClass);
            Object redoObject = getRedoParam.invoke(command);
            Constructor<?> innerRedoClassConstructor =  innerRedoClass.getConstructors()[0];
            Class<?>[] parameterTypes = innerRedoClassConstructor.getParameterTypes();
            if (redoObject != null) {
                Method setParam = innerRedoClass.getMethod("setParam", parameterTypes);
                setParam.invoke(redoObject, redoArgs);
            } else {
                Object redoInstance = innerRedoClassConstructor.newInstance(redoArgs);
                setRedoParam.invoke(command, redoInstance);
            }
            LogUtils.d("saveOperate: cast = "+(System.nanoTime() - start));
        } catch (Exception e) {
            LogUtils.e(e);
        }
    }
}
