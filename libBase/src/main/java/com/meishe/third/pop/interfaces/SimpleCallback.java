package com.meishe.third.pop.interfaces;

/**
 * Description:
 * Create by dance, at 2019/6/13
 * 简单的回调
 * A simple callback
 */
public class SimpleCallback implements XPopupCallback {
    @Override
    public void onCreated() {

    }
    @Override
    public void beforeShow() {

    }

    @Override
    public void onShow() {

    }
    @Override
    public void onDismiss() {

    }
    @Override
    public boolean onBackPressed() {
        return false;
    }
}
