package com.meishe.myvideo.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.meishe.base.manager.LinearLayoutManagerWrapper;
import com.meishe.base.utils.ResourceUtils;
import com.meishe.base.utils.SizeUtils;
import com.meishe.base.view.decoration.ItemDecoration;
import com.meishe.engine.interf.IBaseInfo;
import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.CanvasBlurInfo;
import com.meishe.myvideo.interfaces.BottomEventListener;
import com.meishe.third.adpater.BaseQuickAdapter;
import com.meishe.third.adpater.BaseViewHolder;

import java.util.ArrayList;

/**
 * 画布模糊
 * The canvas of fuzzy
 */
public class MYCanvasBlur extends RelativeLayout {
    private final static int GRADE_COUNT = 4;
    private final int MAX_BLUR_STRENGTH = 64;
    private ImageView mIvConfirm;
    private CanvasBlurAdapter mAdapter;
    private TextView mTvApplyAll;
    private ImageView mIvApplyAll;
    private BottomEventListener mEventListener;

    public MYCanvasBlur(Context context) {
        this(context, null);
    }

    public MYCanvasBlur(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYCanvasBlur(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
        initData();
    }

    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_canvas_blur, this);
        RecyclerView rvBlurList = view.findViewById(R.id.recyclerView);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mIvApplyAll = view.findViewById(R.id.iv_apply_all);
        mTvApplyAll = view.findViewById(R.id.tv_apply_all);

        LinearLayoutManager gridLayoutManager = new LinearLayoutManagerWrapper(getContext(), LinearLayoutManager.HORIZONTAL, false);
        rvBlurList.setLayoutManager(gridLayoutManager);
        mAdapter = new CanvasBlurAdapter();
        rvBlurList.setAdapter(mAdapter);
        rvBlurList.addItemDecoration(new ItemDecoration(SizeUtils.dp2px(3), SizeUtils.dp2px(12)));
    }

    private void initData() {
        ArrayList<IBaseInfo> blurList = new ArrayList<>();
        IBaseInfo canvasBlurInfo = new CanvasBlurInfo();
        canvasBlurInfo.setCoverPath(ResourceUtils.getMipmapToUri(R.mipmap.ic_blur_no));
        blurList.add(canvasBlurInfo);
        for (int i = 1; i < GRADE_COUNT + 1; i++) {
            canvasBlurInfo = new CanvasBlurInfo();
            canvasBlurInfo.setName(i + "");
            canvasBlurInfo.setEffectStrength(getStrength(i));
            blurList.add(canvasBlurInfo);
        }
        mAdapter.setNewData(blurList);
    }

    /**
     * Init listener.
     * 初始化监听
     */
    public void initListener() {
        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
        mIvApplyAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onItemClick(mAdapter.getSelected(), true);
                }
            }
        });
        mTvApplyAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onItemClick(mAdapter.getSelected(), true);
                }
            }
        });
        mAdapter.setOnItemClickListener(new BaseQuickAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                mAdapter.selected(position);
                if (mEventListener != null) {
                    mEventListener.onItemClick(mAdapter.getItem(position), false);
                }
            }
        });
    }

    private float getStrength(int position) {
        switch (position) {
            case 1:
                return 32;
            case 2:
                return 40;
            case 3:
                return 50;
            case 4:
                return 64;
            default:
                return position * 1.0F / GRADE_COUNT * MAX_BLUR_STRENGTH;
        }
    }

    /**
     * 设置事件监听
     * Sets the listener
     *
     * @param listener the listener
     */
    public void setListener(BottomEventListener listener) {
        mEventListener = listener;
    }

    /**
     * 设置选中
     * Set the selected
     *
     * @param blurValue the selected item blur value 所选项目模糊值
     */
    public void selected(final float blurValue) {
        post(new Runnable() {
            @Override
            public void run() {
                for (int i = 0; i < mAdapter.getData().size(); i++) {
                    IBaseInfo baseInfo = mAdapter.getData().get(i);
                    if (blurValue == baseInfo.getEffectStrength()) {
                        mAdapter.selected(i);
                        return;
                    }
                }
                mAdapter.selected(0);
            }
        });

    }


    private static class CanvasBlurAdapter extends BaseQuickAdapter<IBaseInfo, BaseViewHolder> {
        private int mSelectedPosition = -1;

        private CanvasBlurAdapter() {
            super(R.layout.item_canvas_blur);
        }

        /**
         * 选中某一项
         * Selected item .
         *
         * @param position The index of list
         */
        public void selected(int position) {
            if (mSelectedPosition >= 0) {
                notifyItemChanged(mSelectedPosition);
            }
            mSelectedPosition = position;
            if (position >= 0 && position < getData().size()) {
                notifyItemChanged(position);
            }
        }

        /**
         * Gets selected.
         * 获得选择
         *
         * @return the selected
         */
        public IBaseInfo getSelected() {
            return getItem(mSelectedPosition);
        }

        @Override
        protected void convert(@NonNull BaseViewHolder helper, IBaseInfo item) {
            TextView view = helper.getView(R.id.tv_content);
            if (TextUtils.isEmpty(item.getName())) {
                view.setText("");
                view.setBackgroundResource(R.mipmap.ic_blur_no);
            } else {
                view.setText(item.getName());
                view.setBackgroundResource(R.mipmap.ic_blur_strength_bg);
            }
            helper.itemView.setBackgroundResource(helper.getAdapterPosition() == mSelectedPosition ?
                    R.drawable.edit_blur_background_mask : 0);
        }
    }
}
