package com.meishe.myvideo.manager.observer;

/**
 * All rights Reserved, Designed By www.meishesdk.com
 *
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate: 2021/6/11 10:30
 * @Description: 音频录制观察者 The audio record observer
 * @Copyright: www.meishesdk.com Inc. All rights reserved.
 */
public abstract class AudioRecordObserver {

    /**
     * On record start.
     * 记录开始
     *
     * @param id       the id
     * @param filePath the file path
     */
    public void onRecordStart(Long id, String filePath) {
    }

    /**
     * On record progress.
     * 记录进度
     *
     * @param wave   the wave
     * @param during the during
     * @param path   the path
     */
    public void onRecordProgress(float[] wave, int during, String path) {

    }

    /**
     * On record fail.
     * 记录失败
     *
     * @param msg the msg
     */
    public void onRecordFail(String msg) {

    }

    /**
     * On record end.
     * 记录结束
     */
    public void onRecordEnd() {

    }
}
