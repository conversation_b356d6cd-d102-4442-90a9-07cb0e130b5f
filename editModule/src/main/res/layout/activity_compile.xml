<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black_1010">

    <ImageView
        app:layout_constraintVertical_weight="1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:id="@+id/iv_compile_back"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_marginStart="@dimen/dp_px_39"
        android:layout_marginTop="@dimen/dp_px_96"
        android:contentDescription="@null"
        android:src="@drawable/nv_compile_close" />

    <ImageView
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:id="@+id/iv_compile_home"
        android:layout_width="@dimen/dp_px_54"
        android:layout_height="@dimen/dp_px_54"
        android:layout_marginTop="@dimen/dp_px_96"
        android:layout_marginEnd="@dimen/dp_px_39"
        android:background="@mipmap/ic_compile_home"
        android:contentDescription="@null"
        android:visibility="gone" />

    <!--封面-->
    <ImageView
        android:layout_marginTop="@dimen/dp_px_76"
        app:layout_constraintTop_toBottomOf="@+id/iv_compile_back"
        android:id="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_660"
        android:layout_gravity="center_horizontal"
        android:contentDescription="@null" />

    <TextView
        app:layout_constraintTop_toBottomOf="@+id/iv_cover"
        android:id="@+id/tv_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_172"
        android:gravity="center"
        android:text="@string/saved"
        android:textColor="@color/white_8"
        android:textSize="@dimen/sp_px_36"
        android:visibility="gone"/>
    <ScrollView
        android:id="@+id/scrollView"
        app:layout_constraintBottom_toTopOf="@+id/tv_size"
        app:layout_constraintTop_toBottomOf="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_marginStart="@dimen/dp_px_105"
        android:layout_marginEnd="@dimen/dp_px_105"
        android:layout_marginBottom="@dimen/dp_px_45"
        android:layout_height="@dimen/dp300">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/ll_params"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <!--分辨率-->
            <TextView
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginTop="@dimen/dp_px_159"
                android:id="@+id/tv_resolution_title"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="@dimen/dp_px_70"
                android:text="@string/video_resolution"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_39" />

            <TextView
                app:layout_constraintTop_toTopOf="@+id/tv_resolution_title"
                app:layout_constraintBottom_toBottomOf="@+id/tv_resolution_title"
                app:layout_constraintRight_toRightOf="parent"
                android:id="@+id/tv_resolution_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/dp_px_165"
                android:text="@string/video_resolution_info"
                android:textColor="@color/color_ffffa4a4"
                android:textSize="@dimen/sp_px_30" />

            <com.meishe.base.view.CustomCompileParamView
                app:layout_constraintTop_toBottomOf="@+id/tv_resolution_hint"
                android:id="@+id/custom_resolution"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_px_44" />

            <!--帧率-->
            <TextView
                android:id="@+id/tv_frame_rate_title"
                app:layout_constraintTop_toBottomOf="@+id/custom_resolution"
                app:layout_constraintLeft_toLeftOf="@+id/tv_resolution_title"
                android:layout_marginTop="@dimen/dp_px_75"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/video_frame_rate"
                android:textColor="@color/white"
                android:textSize="@dimen/sp_px_42" />

            <TextView
                android:id="@+id/tv_frame_rate_hint"
                app:layout_constraintTop_toTopOf="@+id/tv_frame_rate_title"
                app:layout_constraintBottom_toBottomOf="@+id/tv_frame_rate_title"
                app:layout_constraintRight_toRightOf="@+id/tv_resolution_hint"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/video_frame_rate_info"
                android:textColor="@color/color_ffffa4a4"
                android:textSize="@dimen/sp_px_30" />

            <com.meishe.base.view.CustomCompileParamView
                app:layout_constraintTop_toBottomOf="@+id/tv_frame_rate_hint"
                android:id="@+id/custom_frame_rate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_px_44" />

            <TextView
                android:id="@+id/tv_hdr_export_setting"
                app:layout_constraintTop_toBottomOf="@+id/custom_frame_rate"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginTop="@dimen/dp_px_72"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_px_39"
                android:textColor="@color/white"
                android:text="@string/compile_hdr_export_setting"/>

            <TextView
                android:id="@+id/tv_export_switch_to_HEVC"
                app:layout_constraintTop_toBottomOf="@+id/tv_hdr_export_setting"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginTop="@dimen/dp_px_51"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_px_36"
                android:textColor="@color/white_8"
                android:text="@string/compile_hdr_export_switch_to_HEVC"/>

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switch_to_HEVC"
                android:theme="@style/switchStyle"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_export_switch_to_HEVC"
                app:layout_constraintBottom_toBottomOf="@+id/tv_export_switch_to_HEVC"
                android:layout_marginEnd="@dimen/dp_px_101"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp_px_45"
                android:background="@null"
                app:track="@drawable/switch_track_selector"
                app:switchPadding="@dimen/dp5"
                android:thumb="@drawable/switch_thumb_selector"/>

            <TextView
                android:id="@+id/tv_export_config"
                app:layout_constraintTop_toBottomOf="@+id/tv_export_switch_to_HEVC"
                app:layout_constraintLeft_toLeftOf="parent"
                android:layout_marginTop="@dimen/dp_px_51"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/sp_px_36"
                android:textColor="@color/white_8"
                android:text="@string/compile_hdr_export_config"/>

            <LinearLayout
                android:id="@+id/ll_export_config_none"
                app:layout_constraintTop_toBottomOf="@+id/tv_export_config"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/ll_export_config_st2084"
                android:layout_marginTop="@dimen/dp_px_44"
                android:layout_width="0dp"
                app:layout_constraintHorizontal_weight="1"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_export_config_none"
                    style="@style/CustomCheckBoxTheme"
                    android:layout_width="@dimen/dp_px_45"
                    android:layout_height="@dimen/dp_px_45"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/setting_radio_button" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_px_15"
                    android:text="@string/compile_hdr_export_config_none"
                    android:textColor="@color/white_8"
                    android:textSize="@dimen/sp_px_33"
                    tools:ignore="RelativeOverlap" />
            </LinearLayout>

            <LinearLayout
                app:layout_constraintTop_toTopOf="@+id/ll_export_config_none"
                app:layout_constraintBottom_toBottomOf="@+id/ll_export_config_none"
                app:layout_constraintLeft_toRightOf="@+id/ll_export_config_none"
                app:layout_constraintRight_toLeftOf="@+id/ll_export_config_hlg"
                android:id="@+id/ll_export_config_st2084"
                android:layout_width="0dp"
                app:layout_constraintHorizontal_weight="1"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_export_config_st2084"
                    style="@style/CustomCheckBoxTheme"
                    android:layout_width="@dimen/dp_px_45"
                    android:layout_height="@dimen/dp_px_45"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/setting_radio_button" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_px_15"
                    android:text="@string/compile_hdr_export_config_st2084"
                    android:textColor="@color/white_8"
                    android:textSize="@dimen/sp_px_33"
                    tools:ignore="RelativeOverlap" />
            </LinearLayout>

            <LinearLayout
                app:layout_constraintTop_toTopOf="@+id/ll_export_config_none"
                app:layout_constraintBottom_toBottomOf="@+id/ll_export_config_none"
                app:layout_constraintLeft_toRightOf="@+id/ll_export_config_st2084"
                app:layout_constraintRight_toLeftOf="@+id/ll_export_config_hdr10plus"
                android:id="@+id/ll_export_config_hlg"
                android:layout_width="0dp"
                app:layout_constraintHorizontal_weight="1"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_export_config_hlg"
                    style="@style/CustomCheckBoxTheme"
                    android:layout_width="@dimen/dp_px_45"
                    android:layout_height="@dimen/dp_px_45"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/setting_radio_button" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_px_15"
                    android:text="@string/compile_hdr_export_config_hlg"
                    android:textColor="@color/white_8"
                    android:textSize="@dimen/sp_px_33"
                    tools:ignore="RelativeOverlap" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_export_config_hdr10plus"
                app:layout_constraintTop_toTopOf="@+id/ll_export_config_none"
                app:layout_constraintBottom_toBottomOf="@+id/ll_export_config_none"
                app:layout_constraintLeft_toRightOf="@+id/ll_export_config_hlg"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_width="0dp"
                app:layout_constraintHorizontal_weight="1"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_export_config_hdr10plus"
                    style="@style/CustomCheckBoxTheme"
                    android:layout_width="@dimen/dp_px_45"
                    android:layout_height="@dimen/dp_px_45"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/setting_radio_button" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_px_15"
                    android:text="@string/compile_hdr_export_config_hdr10plus"
                    android:textColor="@color/white_8"
                    android:textSize="@dimen/sp_px_33"
                    tools:ignore="RelativeOverlap" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
    <Button
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/tv_compile"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_100"
        android:layout_marginStart="@dimen/dp_px_165"
        android:layout_marginEnd="@dimen/dp_px_165"
        android:text="@string/compile_out"
        android:layout_marginBottom="@dimen/dp_px_110"
        android:background="@mipmap/ic_cloud_compile_button_bg"
        android:textColor="@color/white" />
    <TextView
        app:layout_constraintBottom_toTopOf="@+id/tv_compile"
        android:id="@+id/tv_size"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="@dimen/dp_px_54"
        android:gravity="center"
        android:textColor="@color/white_9f9f"
        android:textSize="@dimen/sp_px_36" />
    <androidx.constraintlayout.widget.ConstraintLayout
        app:layout_constraintTop_toBottomOf="@+id/iv_cover"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/fl_compile_progress"
        android:layout_marginTop="@dimen/dp_px_840"
        android:visibility="gone">
        <com.meishe.myvideo.view.editview.CompileProgress
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="@dimen/dp_px_256"
            android:id="@+id/edit_compile_progress"
            android:layout_width="@dimen/dp_px_270"
            android:layout_height="@dimen/dp_px_270"
            android:layout_centerHorizontal="true"
            app:progressBackgroundColor="@color/gray_4a4a"
            app:progressColor="@color/red_fc2b"
            app:progressWidth="@dimen/dp_px_15"/>

        <TextView
            app:layout_constraintTop_toTopOf="@+id/edit_compile_progress"
            app:layout_constraintBottom_toBottomOf="@+id/edit_compile_progress"
            app:layout_constraintLeft_toLeftOf="@+id/edit_compile_progress"
            app:layout_constraintRight_toRightOf="@+id/edit_compile_progress"
            android:id="@+id/tv_compile_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_48" />

        <TextView
            app:layout_constraintTop_toBottomOf="@+id/edit_compile_progress"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/tv_compile_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_px_95"
            android:gravity="center"
            android:maxWidth="@dimen/dp_px_900"
            android:text="@string/edit_compile_info"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_36" />

        <Button
            app:layout_constraintTop_toBottomOf="@+id/tv_compile_info"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:id="@+id/bt_compile_cancel"
            android:layout_width="@dimen/dp_px_240"
            android:layout_height="@dimen/dp_px_105"
            android:layout_marginTop="@dimen/dp_px_90"
            android:background="@color/black_2a2a"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_42" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>