package com.meishe.engine.observer;

import android.database.Observable;

import com.meishe.base.utils.ThreadUtils;
import com.meishe.engine.DownloadManager;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/11 18:39
 * @Description :上传被监听对象 The observable for uploading.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DownloadObservable extends Observable<DownLoadObserver> {

    public void notifyStateChanged(final int count){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onStateChanged(count);
                }
            }
        });
    }

    public void notifyProgress(final String tag, final int progress){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onProgress(tag, progress);
                }
            }
        });
    }

    public void notifySuccess(final String tag, final DownloadManager.DownloadParam param){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onSuccess(tag, param);
                }
            }
        });
    }

    public void notifyFailed(final String tag){
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                for (int i = mObservers.size() - 1; i >= 0; i--) {
                    mObservers.get(i).onFailed(tag);
                }
            }
        });
    }
}
