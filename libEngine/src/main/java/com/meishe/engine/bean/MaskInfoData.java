package com.meishe.engine.bean;

/**
 * The type Mask info data.
 * 此类类型为 蒙版信息数据类
 *
 * <AUTHOR>
 * @date :2020/9/18 15:23
 * @des : 蒙版的列表使用的数据源 Data source used by the list of masks
 */
public class MaskInfoData extends BaseInfo {

    /**
     * The mask type
     * 蒙版类型
     */
    private int makType;


    /**
     * Gets type.
     * 获取类型
     *
     * @return the type
     */
    public int getMaskType() {
        return makType;
    }

    /**
     * Sets type.
     * 设置类型
     *
     * @param mType the m type
     */
    public void setMaskType(int mType) {
        this.makType = mType;
    }


}
