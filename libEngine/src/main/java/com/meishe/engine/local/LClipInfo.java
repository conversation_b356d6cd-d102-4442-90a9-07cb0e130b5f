package com.meishe.engine.local;

import androidx.annotation.Nullable;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2020/7/3 17:39
 */
public class LClipInfo extends LNvsObject implements Cloneable, Comparable<LClipInfo>, Serializable {
    /**
     * 客户端不关注index，而是根据inpoint查找。
     * The client does not pay attention to the index, but searches according to the inpoint.
     */
    private int index;
    /**
     * 必传
     * Must
     */
    protected String type = "base";
    private long inPoint;
    private long outPoint;
    /**
     * 关键帧集合
     * The key frame list
     */
    @SerializedName("keyFrameList")
    private List<LMeicamKeyFrame> keyFrameList;

    /**
     * 创建的对象标记
     * The tag of created object
     */
    private String createTag;

    public LClipInfo() {
    }

    public LClipInfo(String type) {
        this.type = type;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public long getOutPoint() {
        return outPoint;
    }

    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    public List<LMeicamKeyFrame> getKeyFrameList() {
        return keyFrameList;
    }

    public void setKeyFrameList(List<LMeicamKeyFrame> keyFrameList) {
        this.keyFrameList = keyFrameList;
    }

    public void setzValue(float zValue){

    }

    public float getzValue() {
        return 0;
    }

    public String getCreateTag() {
        return createTag;
    }

    public void setCreateTag(String createTag) {
        this.createTag = createTag;
    }

    @Override
    public int compareTo(LClipInfo clipInfo) {
        if (inPoint < clipInfo.getInPoint()) {
            return -1;
        } else if (inPoint > clipInfo.getInPoint()) {
            return 1;
        }
        return 0;
    }

    @Override
    public boolean equals(@Nullable Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        LClipInfo tmp = (LClipInfo) obj;
        return tmp.getInPoint() == this.getInPoint();
    }
}
