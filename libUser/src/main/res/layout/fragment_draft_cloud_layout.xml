<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/btn_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/tv_login_desc"
            android:textColor="@color/color_ffbebebe"
            android:textSize="@dimen/dp_px_36" />

        <TextView
            android:layout_width="@dimen/dp_px_315"
            android:layout_height="@dimen/dp_px_120"
            android:layout_marginTop="@dimen/dp_px_90"
            android:background="@drawable/bg_round_white"
            android:gravity="center"
            android:text="@string/tv_login"
            android:textColor="@color/color_ff242424" />
    </LinearLayout>


    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp_px_90"
        app:layout_constraintTop_toTopOf="parent"
        app:tabIndicator="@null"
        app:tabIndicatorColor="@color/white"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/white"
        app:tabTextAppearance="@style/cloudDraftTabLayoutTextStyle"
        app:tabTextColor="@color/white_5" />

    <com.meishe.base.view.MViewPager
        android:id="@+id/vp_pager"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_px_40"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tab_layout" />
</androidx.constraintlayout.widget.ConstraintLayout>