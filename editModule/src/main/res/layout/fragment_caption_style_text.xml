<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <com.meishe.myvideo.view.MYMultiColorView
        android:id="@+id/multi_color_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp_px_45"
        android:layout_marginLeft="@dimen/dp_px_45"
        android:layout_marginTop="@dimen/dp_px_66"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:text="@string/color_opacity"
            android:textColor="@color/white_8"
            android:textSize="@dimen/sp_px_33" />

        <com.meishe.myvideo.view.MYSeekBarTextView
            android:id="@+id/sb_opacity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginRight="@dimen/dp_px_87"
            android:layout_marginEnd="@dimen/dp_px_87"/>
    </LinearLayout>
</LinearLayout>