package com.meishe.engine.local;

import androidx.annotation.NonNull;

import com.meishe.engine.util.DeepCopyUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static com.meishe.engine.bean.MeicamFxParam.TYPE_BOOLEAN;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_FLOAT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_OBJECT;
import static com.meishe.engine.bean.MeicamFxParam.TYPE_STRING;

/**
 * All rights reserved,Designed by www.meishesdk.com
 * 版权所有:www.meishesdk.com
 *
 * <AUTHOR> LiHangZhou
 * @CreateDate :2021/05/20 10:07
 * @Description : 草稿 track video fx特效数据 The draft data of track video clip fx
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class LMeicamTrackVideoFx extends LNvsObject implements Cloneable, Serializable {
    // builtin package
    protected String type;
    protected String subType;
    protected String classType = "trackVideoFx";

    protected String desc;
    /**
     *  强度 intensity
     */
    protected float intensity = 1;


    protected List<LMeicamFxParam<?>> fxParams = new ArrayList<>();

    /**
     * The info for region effect of fx
     * 特效的区域特效
     */
    private LMeicamMaskRegionInfo regionInfo;

    private long inPoint;

    private long outPoint;

    /**
     * 从timeline复制过来的特效的tag 集合
     * The set of fx from timeline tag
     */
    private Set<String> timelineFxTagSet;

    private int flag;

    public LMeicamTrackVideoFx() {
    }

    public LMeicamTrackVideoFx(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public float getIntensity() {
        return intensity;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getSubType() {
        return subType;
    }

    public void setIntensity(float intensity) {
        if (Float.isNaN(intensity)) {
            return;
        }
        this.intensity = intensity;
    }

    public List<LMeicamFxParam<?>> getMeicamFxParam() {
        return fxParams;
    }

    public void setMeicamFxParam(List<LMeicamFxParam<?>> meicamFxParam) {
        fxParams = meicamFxParam;
    }

    public void setStringVal(String key, String value) {
        LMeicamFxParam<String> param = new LMeicamFxParam<>(TYPE_STRING, key, value);
        fxParams.add(param);
    }

    public String getStringVal(String key) {
        return getVal(String.class, TYPE_STRING, key);
    }

    private <T extends Object> T getVal(Class<T> clazz, String sysType, String key) {
        T t = null;
        for (LMeicamFxParam meicamFxParam : fxParams) {
            if (sysType.equals(meicamFxParam.getType()) && key.equals(meicamFxParam.getKey())) {
                t = (T) meicamFxParam.getValue();
            }
        }
        return t;
    }

    public float getFloatVal(String key) {
        return getVal(Float.class, TYPE_FLOAT, key);
    }

    public void setBooleanVal(String key, boolean value) {
        LMeicamFxParam<Boolean> param = new LMeicamFxParam<>(TYPE_BOOLEAN, key, value);
        fxParams.add(param);
    }

    public void setFloatVal(String key, float value) {
        LMeicamFxParam<Float> param = new LMeicamFxParam<>(TYPE_FLOAT, key, value);
        fxParams.add(param);
    }

    public <T> void setObjectVal(String key, T value) {
        LMeicamFxParam<T> param = new LMeicamFxParam<>(TYPE_OBJECT, key, value);
        fxParams.add(param);
    }

    @NonNull
    @Override
    public LMeicamTrackVideoFx clone() {
        return (LMeicamTrackVideoFx) DeepCopyUtil.deepClone(this);
    }

    public long getInPoint() {
        return inPoint;
    }

    public void setInPoint(long inPoint) {
        this.inPoint = inPoint;
    }

    public long getOutPoint() {
        return outPoint;
    }

    public void setOutPoint(long outPoint) {
        this.outPoint = outPoint;
    }

    public LMeicamMaskRegionInfo getRegionInfo() {
        return regionInfo;
    }

    public void setRegionInfo(LMeicamMaskRegionInfo regionInfo) {
        this.regionInfo = regionInfo;
    }

    public Set<String> getTimelineFxTagSet() {
        return timelineFxTagSet;
    }

    public void setTimelineFxTagSet(Set<String> timelineFxTag) {
        this.timelineFxTagSet = timelineFxTag;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }
}
