package com.meishe.myvideo.view;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meishe.myvideo.R;
import com.meishe.myvideo.bean.ColorInfo;
import com.meishe.myvideo.interfaces.BottomEventListener;


/**
 * 画布颜色
 * canvas color
 */
public class MYCanvasColor extends RelativeLayout {

    private ImageView mIvConfirm;
    private MYMultiColorView mColorPicker;
    private TextView mTvApplyAll;
    private ImageView mIvApplyAll;
    private BottomEventListener mEventListener;

    public MYCanvasColor(Context context) {
        this(context, null);
    }

    public MYCanvasColor(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MYCanvasColor(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initListener();
    }

    private void initView() {
        View view = LayoutInflater.from(getContext()).inflate(R.layout.view_canvas_color, this);
        mIvApplyAll = view.findViewById(R.id.iv_apply_all);
        mTvApplyAll = view.findViewById(R.id.tv_apply_all);
        mIvConfirm = view.findViewById(R.id.iv_confirm);
        mColorPicker = view.findViewById(R.id.multi_color_view);
    }


    private void initListener() {
        mIvConfirm.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mEventListener != null) {
                    mEventListener.onDismiss(true);
                }
            }
        });
        mIvApplyAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ColorInfo selectedColor = mColorPicker.getSelectedColor();
                if (mEventListener != null && selectedColor != null && !TextUtils.isEmpty(selectedColor.getCommonInfo())) {
                    mEventListener.onItemClick(selectedColor, true);
                }
            }
        });
        mTvApplyAll.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                ColorInfo selectedColor = mColorPicker.getSelectedColor();
                if (mEventListener != null && selectedColor != null && !TextUtils.isEmpty(selectedColor.getCommonInfo())) {
                    mEventListener.onItemClick(selectedColor, true);
                }
            }
        });
        mColorPicker.setColorClickListener(new MYMultiColorView.OnColorClickListener() {
            @Override
            public void onClick(ColorInfo colorInfo) {
                if (mEventListener != null && colorInfo != null && !TextUtils.isEmpty(colorInfo.getCommonInfo())) {
                    mEventListener.onItemClick(colorInfo, false);
                }
            }
        });
    }

    /**
     * 设置监听
     * Sets listener
     *
     * @param listener the listener
     */
    public void setListener(BottomEventListener listener) {
        mEventListener = listener;
    }

    /**
     * 选中颜色值
     * Selected the color value
     *
     * @param colorValue the color value
     */
    public void selectedColor(final String colorValue) {
        post(new Runnable() {
            @Override
            public void run() {
                mColorPicker.selected(colorValue);
            }
        });
    }

}
