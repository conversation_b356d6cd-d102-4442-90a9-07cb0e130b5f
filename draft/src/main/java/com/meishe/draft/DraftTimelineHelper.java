package com.meishe.draft;

import android.text.TextUtils;

import com.meishe.base.utils.CommonUtils;
import com.meishe.base.utils.FileIOUtils;
import com.meishe.base.utils.GsonUtils;
import com.meishe.base.utils.ThreadUtils;
import com.meishe.draft.data.DraftData;
import com.meishe.draft.db.DraftDbManager;
import com.meishe.draft.db.ProjectDao;
import com.meishe.draft.db.ResourceEntity;
import com.meishe.engine.EditorEngine;
import com.meishe.engine.bean.bridges.FileInfoBridge;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights reserved,Designed by www.meishesdk.com
 *
 * <AUTHOR> yangtailin
 * @CreateDate :2022/3/15 17:11
 * @Description :草稿转时间线帮助类 The helper for draft timeline.
 * @Copyright :www.meishesdk.com Inc.All rights reserved.
 */
public class DraftTimelineHelper {

    /**
     * Recover timeline from draft.
     * 从草稿恢复timeline
     *
     * @param data     the data 草稿数据
     * @param callback the callback 回调
     */
    public static void recoverTimelineFromDraft(final DraftData data, final EditorEngine.TimelineCreateCallback callback) {
        final String jsonData = data.getJsonData();
        FileInfoBridge.sCurrentProject = data.getProjectId();
        if (TextUtils.isEmpty(jsonData)) {
            final DraftData.CloudInfo cloudInfo = data.getCloudInfo();
            if (!FileInfoBridge.has(data.getProjectId())) {
                ThreadUtils.getSinglePool().execute(new Runnable() {
                    @Override
                    public void run() {
                        ProjectDao projectDao = DraftDbManager.get().getProjectDao();
                        List<ResourceEntity> resource = projectDao.getResource(data.getProjectId());
                        if (!CommonUtils.isEmpty(resource)) {
                            for (ResourceEntity entity : resource) {
                                FileInfoBridge.putFileInFo(data.getProjectId(), entity.create());
                            }
                        }
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                doRecoverTimeline(data, cloudInfo, callback);
                            }
                        });
                    }
                });
            } else {
                doRecoverTimeline(data, cloudInfo, callback);
            }
        } else {
            if (!FileInfoBridge.has(data.getProjectId())) {
                ThreadUtils.getSinglePool().execute(new Runnable() {
                    @Override
                    public void run() {
                        ProjectDao projectDao = DraftDbManager.get().getProjectDao();
                        List<ResourceEntity> resource = projectDao.getResource(data.getProjectId());
                        if (!CommonUtils.isEmpty(resource)) {
                            for (ResourceEntity entity : resource) {
                                FileInfoBridge.putFileInFo(data.getProjectId(), entity.create());
                            }
                        }
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (callback != null) {
                                    callback.onTimelineCreate(EditorEngine.getInstance().recoverTimeline(jsonData));
                                }
                            }
                        });
                    }
                });
            } else {
                if (callback != null) {
                    callback.onTimelineCreate(EditorEngine.getInstance().recoverTimeline(jsonData));
                }
            }
        }
    }

    private static void doRecoverTimeline(DraftData data, DraftData.CloudInfo cloudInfo, EditorEngine.TimelineCreateCallback callback) {
        recoverTimelineFromCloudDraft(cloudInfo.infoPath, cloudInfo.cloudToLocalMapInfo, cloudInfo.templatePath, data.getProjectId(),
                String.valueOf(data.getCloudLastModifyTimeLong()), callback);
    }

    /**
     * Recover timeline from draft.
     * 从草稿恢复timeline
     *
     * @param callback the callback 回调
     */
    private static void recoverTimelineFromCloudDraft(String infoJsonPath, String cloudToLocalMapInfo,
                                                      String templatePath, String projectId, String lastModifyTime,
                                                      EditorEngine.TimelineCreateCallback callback) {
        String jsonData = FileIOUtils.readFile2String(infoJsonPath, "utf-8");
        if (TextUtils.isEmpty(jsonData)) {
            if (callback != null) {
                callback.onTimelineCreate(null);
            }
            return;
        }
        List<String> innerAssets = new ArrayList<>();
        CloudToLocalMap cloudToLocalMap = GsonUtils.fromJson(cloudToLocalMapInfo, CloudToLocalMap.class);
        if (cloudToLocalMap != null) {
            List<CloudToLocalMap.Item> cloudToLocalList = cloudToLocalMap.getCloudToLocalList();
            if (!CommonUtils.isEmpty(cloudToLocalList)) {
                for (CloudToLocalMap.Item item : cloudToLocalList) {
                    if (item.isAssets) {
                        innerAssets.add(item.path);
                    }
                }
            }
        }

        EditorEngine.getInstance().recoverFromCloudDraft(templatePath, innerAssets, projectId, lastModifyTime, callback);
    }

    public class CloudToLocalMap implements Serializable {
        List<Item> cloudToLocalList = new ArrayList<>();
        public List<Item> getCloudToLocalList() {
            return cloudToLocalList;
        }

        public void setCloudToLocalList(List<Item> cloudToLocalList) {
            this.cloudToLocalList = cloudToLocalList;
        }

        public class Item implements Serializable {
            public String url;
            public String path;
            public boolean isAssets;

            public Item(String url, String path, boolean isAssets) {
                this.url = url;
                this.path = path;
                this.isAssets = isAssets;
            }
        }
    }
}
